import React from 'react';
import {NavigationContainer} from '@react-navigation/native';
import {createStackNavigator} from '@react-navigation/stack';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {StatusBar, Platform} from 'react-native';

// Screens
import Dashboard from './src/screens/Dashboard';
import Product from './src/screens/Product';
import Cart from './src/screens/Cart';
import Category from './src/screens/Category';
import Search from './src/screens/Search';
import Profile from './src/screens/Profile';
import Login from './src/screens/Login';
import Signup from './src/screens/Signup';
import Onboarding from './src/screens/Onboarding';
import Wishlist from './src/screens/Wishlist';
import Orders from './src/screens/Orders';
import Settings from './src/screens/Settings';

// Components
import {ThemeProvider} from './src/components/ThemeProvider';
import {ToastManager} from './src/components/ToastManager';

// Icons
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
  faHome,
  faSearch,
  faShoppingCart,
  faUser,
  faHeart,
} from '@fortawesome/free-solid-svg-icons';

// Stores
import {AuthStore} from './src/store/auth';
import {ProductStore} from './src/store/product';

// Styles
import {theme} from './src/styles/theme';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Bottom Tab Navigator
function TabNavigator() {
  const {
    state: {cart},
  } = ProductStore;

  return (
    <Tab.Navigator
      screenOptions={({route}) => ({
        tabBarIcon: ({focused, color, size}) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = faHome;
          } else if (route.name === 'Search') {
            iconName = faSearch;
          } else if (route.name === 'Cart') {
            iconName = faShoppingCart;
          } else if (route.name === 'Profile') {
            iconName = faUser;
          } else if (route.name === 'Wishlist') {
            iconName = faHeart;
          }

          return <FontAwesomeIcon icon={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.textSecondary,
        tabBarStyle: {
          backgroundColor: theme.colors.surface,
          borderTopColor: theme.colors.border,
          height: Platform.OS === 'ios' ? 90 : 70,
          paddingBottom: Platform.OS === 'ios' ? 30 : 10,
          paddingTop: 10,
        },
        headerShown: false,
      })}>
      <Tab.Screen
        name="Home"
        component={Dashboard}
        options={{
          tabBarLabel: 'الرئيسية',
        }}
      />
      <Tab.Screen
        name="Search"
        component={Search}
        options={{
          tabBarLabel: 'البحث',
        }}
      />
      <Tab.Screen
        name="Cart"
        component={Cart}
        options={{
          tabBarLabel: 'السلة',
          tabBarBadge: cart.length > 0 ? cart.length : null,
        }}
      />
      <Tab.Screen
        name="Wishlist"
        component={Wishlist}
        options={{
          tabBarLabel: 'المفضلة',
        }}
      />
      <Tab.Screen
        name="Profile"
        component={Profile}
        options={{
          tabBarLabel: 'حسابي',
        }}
      />
    </Tab.Navigator>
  );
}

// Main App Component
export default function App() {
  const {
    state: {isAuthenticated, isFirstTime},
  } = AuthStore;

  return (
    <ThemeProvider>
      <NavigationContainer>
        <StatusBar
          barStyle="light-content"
          backgroundColor={theme.colors.primary}
        />
        <Stack.Navigator
          screenOptions={{
            headerShown: false,
            gestureEnabled: true,
            cardStyleInterpolator: ({current, layouts}) => {
              return {
                cardStyle: {
                  transform: [
                    {
                      translateX: current.progress.interpolate({
                        inputRange: [0, 1],
                        outputRange: [layouts.screen.width, 0],
                      }),
                    },
                  ],
                },
              };
            },
          }}>
          {isFirstTime ? (
            <Stack.Screen name="Onboarding" component={Onboarding} />
          ) : !isAuthenticated ? (
            <>
              <Stack.Screen name="Login" component={Login} />
              <Stack.Screen name="Signup" component={Signup} />
            </>
          ) : (
            <>
              <Stack.Screen name="Main" component={TabNavigator} />
              <Stack.Screen name="Product" component={Product} />
              <Stack.Screen name="Category" component={Category} />
              <Stack.Screen name="Orders" component={Orders} />
              <Stack.Screen name="Settings" component={Settings} />
            </>
          )}
        </Stack.Navigator>
        <ToastManager />
      </NavigationContainer>
    </ThemeProvider>
  );
}
