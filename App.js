import React, {useEffect, useState} from 'react';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {StatusBar, Platform, AppState} from 'react-native';

// المكونات المحسنة
import ThemeProvider from './components/ThemeProvider';
import ErrorBoundary from './components/ErrorBoundary';
import ToastContainer from './components/ToastManager';
import {LoadingOverlay} from './components/LoadingStates';

// Navigation محسن
import AppNavigationImproved from './navigation/AppNavigationImproved';

// Store محسن
import {ProductStoreImproved} from './store/productImproved';
import {toastManager} from './components/ToastManager';
import {observer} from 'mobx-react';

// مكون إدارة حالة التطبيق
const AppStateManager = observer(() => {
    const [appState, setAppState] = useState(AppState.currentState);
    const [isInitialized, setIsInitialized] = useState(false);

    useEffect(() => {
        // تحميل البيانات الأولية
        initializeApp();

        // مراقبة حالة التطبيق
        const subscription = AppState.addEventListener('change', handleAppStateChange);

        return () => subscription?.remove();
    }, []);

    const initializeApp = async () => {
        try {
            // تحميل البيانات الأساسية
            await Promise.all([
                ProductStoreImproved.getCategories(true, false),
                ProductStoreImproved.getProducts({}, true, false)
            ]);

            setIsInitialized(true);
            toastManager.success('تم تحميل التطبيق بنجاح');
        } catch (error) {
            console.error('App initialization error:', error);
            toastManager.error('فشل في تحميل التطبيق');
            setIsInitialized(true); // السماح بالمتابعة حتى لو فشل التحميل
        }
    };

    const handleAppStateChange = (nextAppState) => {
        if (appState.match(/inactive|background/) && nextAppState === 'active') {
            // التطبيق عاد للمقدمة - تحديث البيانات
            ProductStoreImproved.refreshData();
        }
        setAppState(nextAppState);
    };

    if (!isInitialized) {
        return (
            <LoadingOverlay
                visible={true}
                text="جاري تحميل التطبيق..."
            />
        );
    }

    return null;
});

// مكون التطبيق الرئيسي المحسن
const AppImproved = () => {
    return (
        <SafeAreaProvider>
            <ErrorBoundary>
                <ThemeProvider>
                    {/* إعداد شريط الحالة */}
                    <StatusBar
                        barStyle="dark-content"
                        backgroundColor="transparent"
                        translucent={Platform.OS === 'android'}
                    />

                    {/* إدارة حالة التطبيق */}
                    <AppStateManager />

                    {/* التنقل الرئيسي */}
                    <AppNavigationImproved />

                    {/* Toast Container */}
                    <ToastContainer />

                    {/* Loading Overlay عام */}
                    <LoadingOverlay
                        visible={ProductStoreImproved.loadingManager.isLoading}
                        text="جاري المعالجة..."
                    />
                </ThemeProvider>
            </ErrorBoundary>
        </SafeAreaProvider>
    );
};

export default AppImproved;
