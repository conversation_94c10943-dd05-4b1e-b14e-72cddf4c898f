/**
 * Enterprise Service Worker
 * مستوى التطبيقات الكبيرة - دعم offline متقدم
 */

const CACHE_NAME = 'dalila-enterprise-v1.0.0';
const STATIC_CACHE = 'dalila-static-v1.0.0';
const DYNAMIC_CACHE = 'dalila-dynamic-v1.0.0';
const API_CACHE = 'dalila-api-v1.0.0';

// Assets to cache immediately
const STATIC_ASSETS = [
    '/',
    '/web-preview.html',
    '/enterprise_product_system.js',
    '/optimized_product_manager.js',
    // Add more static assets
];

// API endpoints to cache
const API_ENDPOINTS = [
    '/api/v1/ecommerce/products',
    '/api/v1/ecommerce/categories',
    '/api/v1/ecommerce/brands'
];

// Cache strategies
const CACHE_STRATEGIES = {
    CACHE_FIRST: 'cache-first',
    NETWORK_FIRST: 'network-first',
    STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
    NETWORK_ONLY: 'network-only',
    CACHE_ONLY: 'cache-only'
};

// Install event - cache static assets
self.addEventListener('install', (event) => {
    console.log('🔧 Service Worker installing...');
    
    event.waitUntil(
        Promise.all([
            caches.open(STATIC_CACHE).then(cache => {
                return cache.addAll(STATIC_ASSETS);
            }),
            caches.open(API_CACHE),
            caches.open(DYNAMIC_CACHE)
        ]).then(() => {
            console.log('✅ Service Worker installed successfully');
            return self.skipWaiting();
        })
    );
});

// Activate event - clean old caches
self.addEventListener('activate', (event) => {
    console.log('🔧 Service Worker activating...');
    
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== STATIC_CACHE && 
                        cacheName !== DYNAMIC_CACHE && 
                        cacheName !== API_CACHE) {
                        console.log('🗑️ Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('✅ Service Worker activated');
            return self.clients.claim();
        })
    );
});

// Fetch event - handle all network requests
self.addEventListener('fetch', (event) => {
    const request = event.request;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Handle different types of requests
    if (isAPIRequest(url)) {
        event.respondWith(handleAPIRequest(request));
    } else if (isStaticAsset(url)) {
        event.respondWith(handleStaticAsset(request));
    } else {
        event.respondWith(handleDynamicRequest(request));
    }
});

// Handle API requests with advanced caching
async function handleAPIRequest(request) {
    const url = new URL(request.url);
    const cacheKey = generateCacheKey(request);
    
    try {
        // For product searches, use stale-while-revalidate
        if (url.pathname.includes('/products')) {
            return await staleWhileRevalidate(request, API_CACHE);
        }
        
        // For other API calls, use network-first
        return await networkFirst(request, API_CACHE);
        
    } catch (error) {
        console.error('API request failed:', error);
        
        // Return cached version if available
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline fallback
        return createOfflineResponse(request);
    }
}

// Handle static assets with cache-first strategy
async function handleStaticAsset(request) {
    try {
        return await cacheFirst(request, STATIC_CACHE);
    } catch (error) {
        console.error('Static asset request failed:', error);
        return new Response('Asset not available offline', { status: 404 });
    }
}

// Handle dynamic requests
async function handleDynamicRequest(request) {
    try {
        return await staleWhileRevalidate(request, DYNAMIC_CACHE);
    } catch (error) {
        console.error('Dynamic request failed:', error);
        
        // Return cached HTML for navigation requests
        if (request.mode === 'navigate') {
            const cachedHTML = await caches.match('/web-preview.html');
            if (cachedHTML) {
                return cachedHTML;
            }
        }
        
        return new Response('Page not available offline', { status: 404 });
    }
}

// Cache strategies implementation

async function cacheFirst(request, cacheName) {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
        return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
        cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
}

async function networkFirst(request, cacheName) {
    const cache = await caches.open(cacheName);
    
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        const cachedResponse = await cache.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        throw error;
    }
}

async function staleWhileRevalidate(request, cacheName) {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    // Start network request in background
    const networkPromise = fetch(request).then(response => {
        if (response.ok) {
            cache.put(request, response.clone());
        }
        return response;
    }).catch(error => {
        console.warn('Background fetch failed:', error);
    });
    
    // Return cached response immediately if available
    if (cachedResponse) {
        return cachedResponse;
    }
    
    // Wait for network response if no cache
    return networkPromise;
}

// Utility functions

function isAPIRequest(url) {
    return url.pathname.startsWith('/api/') || 
           url.hostname === 'dalilakauto.com';
}

function isStaticAsset(url) {
    const staticExtensions = ['.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico'];
    return staticExtensions.some(ext => url.pathname.endsWith(ext)) ||
           STATIC_ASSETS.includes(url.pathname);
}

function generateCacheKey(request) {
    const url = new URL(request.url);
    
    // For API requests, include relevant query parameters
    if (isAPIRequest(url)) {
        const relevantParams = ['page', 'per_page', 'category', 'search'];
        const params = new URLSearchParams();
        
        relevantParams.forEach(param => {
            if (url.searchParams.has(param)) {
                params.set(param, url.searchParams.get(param));
            }
        });
        
        return `${url.pathname}?${params.toString()}`;
    }
    
    return request.url;
}

function createOfflineResponse(request) {
    const url = new URL(request.url);
    
    if (url.pathname.includes('/products')) {
        return new Response(JSON.stringify({
            error: false,
            data: [],
            message: 'Offline - no cached data available',
            offline: true
        }), {
            headers: { 'Content-Type': 'application/json' },
            status: 200
        });
    }
    
    return new Response('Service unavailable offline', { status: 503 });
}

// Background sync for analytics and errors
self.addEventListener('sync', (event) => {
    if (event.tag === 'analytics-sync') {
        event.waitUntil(syncAnalytics());
    } else if (event.tag === 'error-sync') {
        event.waitUntil(syncErrors());
    }
});

async function syncAnalytics() {
    try {
        // Get analytics data from IndexedDB
        const db = await openIndexedDB();
        const transaction = db.transaction(['analytics'], 'readonly');
        const store = transaction.objectStore('analytics');
        const events = await getAllFromStore(store);
        
        if (events.length > 0) {
            // Send to server
            const response = await fetch('/api/analytics', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ events })
            });
            
            if (response.ok) {
                // Clear sent events
                const deleteTransaction = db.transaction(['analytics'], 'readwrite');
                const deleteStore = deleteTransaction.objectStore('analytics');
                events.forEach(event => deleteStore.delete(event.id));
            }
        }
    } catch (error) {
        console.error('Analytics sync failed:', error);
    }
}

async function syncErrors() {
    try {
        // Similar to analytics sync but for error reports
        console.log('Syncing error reports...');
    } catch (error) {
        console.error('Error sync failed:', error);
    }
}

// Push notifications
self.addEventListener('push', (event) => {
    if (!event.data) return;
    
    const data = event.data.json();
    const options = {
        body: data.body,
        icon: '/icon-192x192.png',
        badge: '/badge-72x72.png',
        data: data.data,
        actions: data.actions || []
    };
    
    event.waitUntil(
        self.registration.showNotification(data.title, options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
    event.notification.close();
    
    const data = event.notification.data;
    
    event.waitUntil(
        clients.openWindow(data.url || '/')
    );
});

// Message handling from main thread
self.addEventListener('message', (event) => {
    const { type, data } = event.data;
    
    switch (type) {
        case 'CACHE_PRODUCTS':
            event.waitUntil(cacheProducts(data.products));
            break;
            
        case 'CLEAR_CACHE':
            event.waitUntil(clearAllCaches());
            break;
            
        case 'GET_CACHE_SIZE':
            event.waitUntil(getCacheSize().then(size => {
                event.ports[0].postMessage({ type: 'CACHE_SIZE', size });
            }));
            break;
    }
});

async function cacheProducts(products) {
    const cache = await caches.open(API_CACHE);
    
    // Cache individual product requests
    products.forEach(product => {
        const request = new Request(`/api/v1/ecommerce/products/${product.id}`);
        const response = new Response(JSON.stringify(product), {
            headers: { 'Content-Type': 'application/json' }
        });
        cache.put(request, response);
    });
}

async function clearAllCaches() {
    const cacheNames = await caches.keys();
    return Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
    );
}

async function getCacheSize() {
    const cacheNames = await caches.keys();
    let totalSize = 0;
    
    for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const requests = await cache.keys();
        
        for (const request of requests) {
            const response = await cache.match(request);
            if (response) {
                const blob = await response.blob();
                totalSize += blob.size;
            }
        }
    }
    
    return totalSize;
}

// IndexedDB helpers
function openIndexedDB() {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('DalilaProductDB', 1);
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
    });
}

function getAllFromStore(store) {
    return new Promise((resolve, reject) => {
        const request = store.getAll();
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
    });
}

// Performance monitoring
self.addEventListener('fetch', (event) => {
    // Track fetch performance
    const startTime = performance.now();
    
    event.respondWith(
        handleRequest(event.request).then(response => {
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            // Send performance data to main thread
            self.clients.matchAll().then(clients => {
                clients.forEach(client => {
                    client.postMessage({
                        type: 'FETCH_PERFORMANCE',
                        data: {
                            url: event.request.url,
                            duration,
                            cached: response.headers.get('x-cache') === 'HIT'
                        }
                    });
                });
            });
            
            return response;
        })
    );
});

async function handleRequest(request) {
    // This is a simplified version - use the specific handlers above
    const url = new URL(request.url);
    
    if (isAPIRequest(url)) {
        return handleAPIRequest(request);
    } else if (isStaticAsset(url)) {
        return handleStaticAsset(request);
    } else {
        return handleDynamicRequest(request);
    }
}

console.log('🔧 Enterprise Service Worker loaded');
