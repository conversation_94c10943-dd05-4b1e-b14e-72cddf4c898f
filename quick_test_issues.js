/**
 * اختبار سريع للمشاكل المكتشفة في تطبيق دليل كار
 * Quick Test for Discovered Issues in Dalila Car App
 */

console.log('🔍 بدء الاختبار السريع للمشاكل...');

// اختبار 1: فحص تضارب معرفات البحث
function testSearchInputConflicts() {
    console.log('\n1️⃣ فحص تضارب معرفات البحث:');
    
    const searchIds = [
        'searchInput',
        'searchPageInput', 
        'blogSearchInput',
        'productSearchInput',
        'categorySearchInput',
        'allProductsSearchInput'
    ];
    
    let foundInputs = 0;
    searchIds.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            foundInputs++;
            console.log(`✅ وُجد: ${id}`);
        } else {
            console.log(`❌ مفقود: ${id}`);
        }
    });
    
    if (foundInputs > 3) {
        console.log(`⚠️ تحذير: وُجد ${foundInputs} حقل بحث - قد يسبب تضارب`);
        return false;
    }
    return true;
}

// اختبار 2: فحص دوال البحث المتعددة
function testSearchFunctionConflicts() {
    console.log('\n2️⃣ فحص دوال البحث المتعددة:');
    
    const searchFunctions = [
        'performSearch',
        'performAdvancedSearch',
        'performEnhancedSearch',
        'performProductSearch'
    ];
    
    let foundFunctions = 0;
    searchFunctions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            foundFunctions++;
            console.log(`✅ وُجدت: ${funcName}()`);
        } else {
            console.log(`❌ مفقودة: ${funcName}()`);
        }
    });
    
    if (foundFunctions > 2) {
        console.log(`⚠️ تحذير: وُجد ${foundFunctions} دالة بحث - قد يسبب تضارب`);
        return false;
    }
    return true;
}

// اختبار 3: فحص العناصر المخفية
function testHiddenElements() {
    console.log('\n3️⃣ فحص العناصر المخفية:');
    
    const hiddenElements = document.querySelectorAll('[style*="display: none"], [style*="display:none"]');
    console.log(`📊 عدد العناصر المخفية: ${hiddenElements.length}`);
    
    if (hiddenElements.length > 50) {
        console.log('⚠️ تحذير: عدد كبير من العناصر المخفية قد يؤثر على الأداء');
        return false;
    }
    return true;
}

// اختبار 4: فحص أخطاء الكونسول
function testConsoleErrors() {
    console.log('\n4️⃣ فحص أخطاء الكونسول:');
    
    // تسجيل الأخطاء
    const originalError = console.error;
    const errors = [];
    
    console.error = function(...args) {
        errors.push(args.join(' '));
        originalError.apply(console, args);
    };
    
    // انتظار قليل لتجميع الأخطاء
    setTimeout(() => {
        console.error = originalError; // استعادة الدالة الأصلية
        
        if (errors.length === 0) {
            console.log('✅ لا توجد أخطاء في الكونسول');
            return true;
        } else {
            console.log(`❌ وُجد ${errors.length} خطأ في الكونسول:`);
            errors.forEach((error, index) => {
                console.log(`   ${index + 1}. ${error}`);
            });
            return false;
        }
    }, 2000);
}

// اختبار 5: فحص الأداء
function testPerformance() {
    console.log('\n5️⃣ فحص الأداء:');
    
    // فحص سرعة التحميل
    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
    console.log(`⏱️ وقت التحميل: ${loadTime}ms`);
    
    if (loadTime > 5000) {
        console.log('❌ وقت التحميل بطيء جداً (أكثر من 5 ثواني)');
        return false;
    } else if (loadTime > 3000) {
        console.log('⚠️ وقت التحميل بطيء (أكثر من 3 ثواني)');
        return false;
    } else {
        console.log('✅ وقت التحميل جيد');
    }
    
    // فحص استخدام الذاكرة
    if (performance.memory) {
        const memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
        console.log(`💾 استخدام الذاكرة: ${memoryUsage.toFixed(2)}MB`);
        
        if (memoryUsage > 100) {
            console.log('❌ استخدام ذاكرة عالي جداً');
            return false;
        } else if (memoryUsage > 50) {
            console.log('⚠️ استخدام ذاكرة مرتفع');
            return false;
        } else {
            console.log('✅ استخدام الذاكرة جيد');
        }
    }
    
    return true;
}

// اختبار 6: فحص البحث
function testSearchFunctionality() {
    console.log('\n6️⃣ فحص وظائف البحث:');
    
    const searchInput = document.getElementById('searchInput');
    if (!searchInput) {
        console.log('❌ حقل البحث الرئيسي غير موجود');
        return false;
    }
    
    // محاكاة البحث
    try {
        searchInput.value = 'هيونداي';
        searchInput.dispatchEvent(new Event('input'));
        console.log('✅ تم تشغيل البحث بنجاح');
        
        // فحص النتائج بعد ثانية
        setTimeout(() => {
            const results = document.querySelectorAll('.product-card');
            if (results.length > 0) {
                console.log(`✅ البحث يعمل - وُجد ${results.length} نتيجة`);
            } else {
                console.log('⚠️ البحث لا يعيد نتائج');
            }
        }, 1000);
        
        return true;
    } catch (error) {
        console.log(`❌ خطأ في البحث: ${error.message}`);
        return false;
    }
}

// اختبار 7: فحص سلة التسوق
function testCartFunctionality() {
    console.log('\n7️⃣ فحص سلة التسوق:');
    
    try {
        // فحص localStorage
        const cart = JSON.parse(localStorage.getItem('cart') || '[]');
        console.log(`📊 عدد المنتجات في السلة: ${cart.length}`);
        
        // فحص دالة إضافة للسلة
        if (typeof addToCart === 'function') {
            console.log('✅ دالة addToCart موجودة');
        } else {
            console.log('❌ دالة addToCart مفقودة');
            return false;
        }
        
        // فحص عداد السلة
        const cartBadge = document.getElementById('cartBadge');
        if (cartBadge) {
            console.log('✅ عداد السلة موجود');
        } else {
            console.log('❌ عداد السلة مفقود');
            return false;
        }
        
        return true;
    } catch (error) {
        console.log(`❌ خطأ في فحص السلة: ${error.message}`);
        return false;
    }
}

// اختبار 8: فحص الاتصال بـ API
async function testAPIConnection() {
    console.log('\n8️⃣ فحص الاتصال بـ API:');
    
    try {
        const response = await fetch('https://dalilakauto.com/api/v1/ecommerce/products?limit=1');
        
        if (response.ok) {
            const data = await response.json();
            if (data.data && data.data.length > 0) {
                console.log('✅ API يعمل بشكل صحيح');
                return true;
            } else {
                console.log('⚠️ API يعيد بيانات فارغة');
                return false;
            }
        } else {
            console.log(`❌ خطأ في API: ${response.status}`);
            return false;
        }
    } catch (error) {
        console.log(`❌ خطأ في الاتصال بـ API: ${error.message}`);
        return false;
    }
}

// تشغيل جميع الاختبارات
async function runQuickTests() {
    console.log('🚀 بدء تشغيل الاختبارات السريعة...\n');
    
    const results = {
        searchConflicts: testSearchInputConflicts(),
        functionConflicts: testSearchFunctionConflicts(),
        hiddenElements: testHiddenElements(),
        performance: testPerformance(),
        searchFunctionality: testSearchFunctionality(),
        cartFunctionality: testCartFunctionality()
    };
    
    // اختبار API (async)
    results.apiConnection = await testAPIConnection();
    
    // اختبار أخطاء الكونسول (مع تأخير)
    testConsoleErrors();
    
    // حساب النتائج
    const totalTests = Object.keys(results).length;
    const passedTests = Object.values(results).filter(result => result === true).length;
    const failedTests = totalTests - passedTests;
    const successRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    console.log('\n📊 ملخص نتائج الاختبار السريع:');
    console.log('=====================================');
    console.log(`✅ نجح: ${passedTests}/${totalTests}`);
    console.log(`❌ فشل: ${failedTests}/${totalTests}`);
    console.log(`📈 معدل النجاح: ${successRate}%`);
    
    if (successRate >= 80) {
        console.log('🎉 التطبيق في حالة جيدة!');
    } else if (successRate >= 60) {
        console.log('⚠️ التطبيق يحتاج لبعض الإصلاحات');
    } else {
        console.log('🚨 التطبيق يحتاج لإصلاحات عاجلة');
    }
    
    console.log('\n🔍 للحصول على تقرير مفصل، راجع ملف: COMPREHENSIVE_ISSUES_REPORT_FINAL.md');
    
    return results;
}

// تشغيل الاختبارات عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runQuickTests);
} else {
    runQuickTests();
}

// تصدير للاستخدام الخارجي
window.runQuickTests = runQuickTests;
