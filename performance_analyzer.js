/**
 * محلل الأداء الشامل لتطبيق دليل كار
 * Comprehensive Performance Analyzer for Dalila Car App
 */

console.log('⚡ بدء تحليل الأداء الشامل...');

// متغيرات تتبع الأداء
let performanceMetrics = {
    loading: {},
    runtime: {},
    memory: {},
    network: {},
    ui: {}
};

// 1. فحص أداء التحميل
function analyzeLoadingPerformance() {
    console.log('\n1️⃣ تحليل أداء التحميل:');
    
    const navigation = performance.getEntriesByType('navigation')[0];
    if (!navigation) {
        console.log('❌ لا يمكن الحصول على بيانات التحميل');
        return false;
    }
    
    // حساب المقاييس المهمة
    const metrics = {
        dns: navigation.domainLookupEnd - navigation.domainLookupStart,
        tcp: navigation.connectEnd - navigation.connectStart,
        request: navigation.responseStart - navigation.requestStart,
        response: navigation.responseEnd - navigation.responseStart,
        domProcessing: navigation.domContentLoadedEventStart - navigation.responseEnd,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        onLoad: navigation.loadEventEnd - navigation.loadEventStart,
        totalTime: navigation.loadEventEnd - navigation.navigationStart
    };
    
    performanceMetrics.loading = metrics;
    
    console.log(`🔍 DNS Lookup: ${metrics.dns.toFixed(2)}ms`);
    console.log(`🔗 TCP Connection: ${metrics.tcp.toFixed(2)}ms`);
    console.log(`📤 Request Time: ${metrics.request.toFixed(2)}ms`);
    console.log(`📥 Response Time: ${metrics.response.toFixed(2)}ms`);
    console.log(`🏗️ DOM Processing: ${metrics.domProcessing.toFixed(2)}ms`);
    console.log(`📄 DOM Content Loaded: ${metrics.domContentLoaded.toFixed(2)}ms`);
    console.log(`⚡ OnLoad Event: ${metrics.onLoad.toFixed(2)}ms`);
    console.log(`⏱️ Total Load Time: ${metrics.totalTime.toFixed(2)}ms`);
    
    // تقييم الأداء
    let score = 100;
    if (metrics.totalTime > 5000) score -= 30;
    else if (metrics.totalTime > 3000) score -= 15;
    
    if (metrics.domProcessing > 2000) score -= 20;
    else if (metrics.domProcessing > 1000) score -= 10;
    
    console.log(`📊 نقاط أداء التحميل: ${score}/100`);
    
    return score >= 70;
}

// 2. فحص أداء الذاكرة
function analyzeMemoryPerformance() {
    console.log('\n2️⃣ تحليل أداء الذاكرة:');
    
    if (!performance.memory) {
        console.log('⚠️ معلومات الذاكرة غير متاحة في هذا المتصفح');
        return true;
    }
    
    const memory = performance.memory;
    const metrics = {
        used: memory.usedJSHeapSize / 1024 / 1024,
        total: memory.totalJSHeapSize / 1024 / 1024,
        limit: memory.jsHeapSizeLimit / 1024 / 1024,
        usage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
    };
    
    performanceMetrics.memory = metrics;
    
    console.log(`💾 الذاكرة المستخدمة: ${metrics.used.toFixed(2)}MB`);
    console.log(`📊 إجمالي الذاكرة: ${metrics.total.toFixed(2)}MB`);
    console.log(`🔒 حد الذاكرة: ${metrics.limit.toFixed(2)}MB`);
    console.log(`📈 نسبة الاستخدام: ${metrics.usage.toFixed(2)}%`);
    
    // تقييم استخدام الذاكرة
    let score = 100;
    if (metrics.used > 100) score -= 40;
    else if (metrics.used > 50) score -= 20;
    else if (metrics.used > 25) score -= 10;
    
    if (metrics.usage > 80) score -= 30;
    else if (metrics.usage > 60) score -= 15;
    
    console.log(`📊 نقاط أداء الذاكرة: ${score}/100`);
    
    return score >= 70;
}

// 3. فحص أداء الشبكة
function analyzeNetworkPerformance() {
    console.log('\n3️⃣ تحليل أداء الشبكة:');
    
    const resources = performance.getEntriesByType('resource');
    
    let totalSize = 0;
    let totalDuration = 0;
    let slowRequests = 0;
    let failedRequests = 0;
    
    const resourceTypes = {
        script: { count: 0, size: 0, duration: 0 },
        stylesheet: { count: 0, size: 0, duration: 0 },
        image: { count: 0, size: 0, duration: 0 },
        fetch: { count: 0, size: 0, duration: 0 },
        other: { count: 0, size: 0, duration: 0 }
    };
    
    resources.forEach(resource => {
        const duration = resource.responseEnd - resource.requestStart;
        const size = resource.transferSize || 0;
        
        totalDuration += duration;
        totalSize += size;
        
        if (duration > 3000) slowRequests++;
        
        // تصنيف الموارد
        let type = 'other';
        if (resource.initiatorType === 'script') type = 'script';
        else if (resource.initiatorType === 'link') type = 'stylesheet';
        else if (resource.initiatorType === 'img') type = 'image';
        else if (resource.initiatorType === 'fetch' || resource.initiatorType === 'xmlhttprequest') type = 'fetch';
        
        resourceTypes[type].count++;
        resourceTypes[type].size += size;
        resourceTypes[type].duration += duration;
    });
    
    performanceMetrics.network = {
        totalRequests: resources.length,
        totalSize: totalSize / 1024 / 1024,
        averageDuration: totalDuration / resources.length,
        slowRequests,
        failedRequests,
        resourceTypes
    };
    
    console.log(`📊 إجمالي الطلبات: ${resources.length}`);
    console.log(`📦 إجمالي الحجم: ${(totalSize / 1024 / 1024).toFixed(2)}MB`);
    console.log(`⏱️ متوسط وقت الاستجابة: ${(totalDuration / resources.length).toFixed(2)}ms`);
    console.log(`🐌 الطلبات البطيئة: ${slowRequests}`);
    
    // تفاصيل أنواع الموارد
    Object.keys(resourceTypes).forEach(type => {
        const data = resourceTypes[type];
        if (data.count > 0) {
            console.log(`   ${type}: ${data.count} ملف، ${(data.size / 1024).toFixed(2)}KB`);
        }
    });
    
    // تقييم أداء الشبكة
    let score = 100;
    if (totalSize > 5 * 1024 * 1024) score -= 30; // أكثر من 5MB
    else if (totalSize > 2 * 1024 * 1024) score -= 15; // أكثر من 2MB
    
    if (slowRequests > 5) score -= 25;
    else if (slowRequests > 2) score -= 10;
    
    if (resources.length > 100) score -= 20;
    else if (resources.length > 50) score -= 10;
    
    console.log(`📊 نقاط أداء الشبكة: ${score}/100`);
    
    return score >= 70;
}

// 4. فحص أداء واجهة المستخدم
function analyzeUIPerformance() {
    console.log('\n4️⃣ تحليل أداء واجهة المستخدم:');
    
    // فحص عدد عناصر DOM
    const domElements = document.querySelectorAll('*').length;
    console.log(`🏗️ عدد عناصر DOM: ${domElements}`);
    
    // فحص العناصر المخفية
    const hiddenElements = document.querySelectorAll('[style*="display: none"], [style*="display:none"]').length;
    console.log(`👻 العناصر المخفية: ${hiddenElements}`);
    
    // فحص الصور
    const images = document.querySelectorAll('img');
    let unoptimizedImages = 0;
    images.forEach(img => {
        if (img.naturalWidth > 1920 || img.naturalHeight > 1080) {
            unoptimizedImages++;
        }
    });
    console.log(`🖼️ إجمالي الصور: ${images.length}`);
    console.log(`⚠️ صور غير محسنة: ${unoptimizedImages}`);
    
    // فحص CSS
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"], style').length;
    console.log(`🎨 ملفات CSS: ${stylesheets}`);
    
    // فحص JavaScript
    const scripts = document.querySelectorAll('script').length;
    console.log(`⚙️ ملفات JavaScript: ${scripts}`);
    
    performanceMetrics.ui = {
        domElements,
        hiddenElements,
        images: images.length,
        unoptimizedImages,
        stylesheets,
        scripts
    };
    
    // تقييم أداء واجهة المستخدم
    let score = 100;
    if (domElements > 3000) score -= 25;
    else if (domElements > 1500) score -= 10;
    
    if (hiddenElements > 100) score -= 20;
    else if (hiddenElements > 50) score -= 10;
    
    if (unoptimizedImages > 10) score -= 15;
    else if (unoptimizedImages > 5) score -= 8;
    
    console.log(`📊 نقاط أداء واجهة المستخدم: ${score}/100`);
    
    return score >= 70;
}

// 5. فحص أداء وقت التشغيل
function analyzeRuntimePerformance() {
    console.log('\n5️⃣ تحليل أداء وقت التشغيل:');
    
    // قياس سرعة تنفيذ العمليات
    const startTime = performance.now();
    
    // محاكاة عمليات شائعة
    const testOperations = {
        domQuery: () => {
            const start = performance.now();
            document.querySelectorAll('.product-card');
            return performance.now() - start;
        },
        
        localStorageRead: () => {
            const start = performance.now();
            localStorage.getItem('cart');
            localStorage.getItem('wishlist');
            localStorage.getItem('user');
            return performance.now() - start;
        },
        
        arrayOperations: () => {
            const start = performance.now();
            const arr = Array.from({length: 1000}, (_, i) => i);
            arr.filter(x => x % 2 === 0);
            arr.map(x => x * 2);
            return performance.now() - start;
        }
    };
    
    const runtimeMetrics = {};
    Object.keys(testOperations).forEach(operation => {
        const duration = testOperations[operation]();
        runtimeMetrics[operation] = duration;
        console.log(`⚡ ${operation}: ${duration.toFixed(2)}ms`);
    });
    
    performanceMetrics.runtime = runtimeMetrics;
    
    const totalRuntime = performance.now() - startTime;
    console.log(`⏱️ إجمالي وقت الاختبار: ${totalRuntime.toFixed(2)}ms`);
    
    // تقييم أداء وقت التشغيل
    let score = 100;
    if (runtimeMetrics.domQuery > 10) score -= 20;
    if (runtimeMetrics.localStorageRead > 5) score -= 15;
    if (runtimeMetrics.arrayOperations > 20) score -= 10;
    
    console.log(`📊 نقاط أداء وقت التشغيل: ${score}/100`);
    
    return score >= 70;
}

// 6. تقرير الأداء الشامل
function generatePerformanceReport() {
    console.log('\n📊 تقرير الأداء الشامل:');
    console.log('=====================================');
    
    const results = {
        loading: analyzeLoadingPerformance(),
        memory: analyzeMemoryPerformance(),
        network: analyzeNetworkPerformance(),
        ui: analyzeUIPerformance(),
        runtime: analyzeRuntimePerformance()
    };
    
    // حساب النقاط الإجمالية
    const totalTests = Object.keys(results).length;
    const passedTests = Object.values(results).filter(result => result === true).length;
    const overallScore = (passedTests / totalTests) * 100;
    
    console.log('\n🎯 النتيجة النهائية:');
    console.log(`✅ اختبارات ناجحة: ${passedTests}/${totalTests}`);
    console.log(`📈 النقاط الإجمالية: ${overallScore.toFixed(1)}/100`);
    
    // تقييم الأداء العام
    let rating = '';
    let recommendations = [];
    
    if (overallScore >= 90) {
        rating = '🏆 ممتاز';
        recommendations.push('الأداء ممتاز! استمر في المراقبة الدورية');
    } else if (overallScore >= 75) {
        rating = '👍 جيد جداً';
        recommendations.push('أداء جيد مع إمكانية تحسينات طفيفة');
    } else if (overallScore >= 60) {
        rating = '⚠️ مقبول';
        recommendations.push('يحتاج لتحسينات في الأداء');
        if (!results.loading) recommendations.push('- تحسين سرعة التحميل');
        if (!results.memory) recommendations.push('- تحسين استخدام الذاكرة');
        if (!results.network) recommendations.push('- تحسين أداء الشبكة');
    } else {
        rating = '🚨 ضعيف';
        recommendations.push('يحتاج لإصلاحات عاجلة في الأداء');
        recommendations.push('- مراجعة شاملة للكود');
        recommendations.push('- تحسين الموارد والصور');
        recommendations.push('- تقليل عدد الطلبات');
    }
    
    console.log(`🏅 التقييم: ${rating}`);
    console.log('\n💡 التوصيات:');
    recommendations.forEach(rec => console.log(`   ${rec}`));
    
    // حفظ التقرير
    window.performanceReport = {
        score: overallScore,
        rating,
        results,
        metrics: performanceMetrics,
        recommendations,
        timestamp: new Date().toISOString()
    };
    
    return window.performanceReport;
}

// تشغيل تحليل الأداء
function runPerformanceAnalysis() {
    console.log('⚡ بدء تحليل الأداء الشامل...\n');
    
    // انتظار قليل للتأكد من تحميل الصفحة
    setTimeout(() => {
        const report = generatePerformanceReport();
        
        console.log('\n📋 تم حفظ التقرير في window.performanceReport');
        console.log('🔍 للحصول على تفاصيل أكثر، استخدم: console.log(window.performanceReport)');
        
        return report;
    }, 1000);
}

// تشغيل التحليل
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runPerformanceAnalysis);
} else {
    runPerformanceAnalysis();
}

// تصدير للاستخدام الخارجي
window.runPerformanceAnalysis = runPerformanceAnalysis;
window.getPerformanceMetrics = () => performanceMetrics;
