import React, {useState, useRef, useEffect} from 'react';
import {
    View,
    ScrollView,
    Text,
    Image,
    TouchableOpacity,
    SafeAreaView,
    StyleSheet,
    Animated,
    Alert,
    FlatList
} from 'react-native';

import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
    faTrash,
    faPlus,
    faMinus,
    faShoppingCart,
    faArrowLeft,
    faHeart,
    faPercent,
    faTruck,
    faShieldAlt,
    faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';

// المكونات المحسنة
import {useTheme} from '../components/ThemeProvider';
import {ProductStoreImproved} from '../store/productImproved';
import {toastManager} from '../components/ToastManager';
import {LoadingSpinner, LoadingOverlay} from '../components/LoadingStates';
import ButtonImproved from '../components/ui/ButtonImproved';

import {observer} from 'mobx-react';

const CartImproved = observer(({navigation}) => {
    // Theme
    const {colors, typography, spacing, shadows} = useTheme();
    
    // Store
    const {
        state: {cart},
        cartTotal,
        cartItemsCount,
        updateCartQuantity,
        removeFromCart,
        clearCart,
        addToWishlist,
        loadingManager
    } = ProductStoreImproved;

    // حالات المكون
    const [processingCheckout, setProcessingCheckout] = useState(false);
    const [couponCode, setCouponCode] = useState('');
    const [discount, setDiscount] = useState(0);
    const [showCouponInput, setShowCouponInput] = useState(false);

    // Animations
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const slideAnim = useRef(new Animated.Value(50)).current;

    useEffect(() => {
        // Animation للظهور
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 500,
                useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 500,
                useNativeDriver: true,
            })
        ]).start();
    }, []);

    // حساب الإجماليات
    const subtotal = cartTotal;
    const shipping = subtotal > 100 ? 0 : 15; // شحن مجاني فوق 100
    const tax = subtotal * 0.1; // ضريبة 10%
    const discountAmount = subtotal * (discount / 100);
    const total = subtotal + shipping + tax - discountAmount;

    // تحديث الكمية
    const handleUpdateQuantity = async (productId, newQuantity) => {
        try {
            await updateCartQuantity(productId, newQuantity);
            
            if (newQuantity === 0) {
                toastManager.info('تم حذف المنتج من السلة');
            } else {
                toastManager.success('تم تحديث الكمية');
            }
        } catch (error) {
            toastManager.error('فشل في تحديث الكمية');
        }
    };

    // حذف من السلة
    const handleRemoveItem = (item) => {
        Alert.alert(
            'حذف المنتج',
            `هل تريد حذف "${item.product.name}" من السلة؟`,
            [
                {text: 'إلغاء', style: 'cancel'},
                {
                    text: 'حذف',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await removeFromCart(item.product.id);
                            toastManager.success('تم حذف المنتج من السلة');
                        } catch (error) {
                            toastManager.error('فشل في حذف المنتج');
                        }
                    }
                }
            ]
        );
    };

    // إضافة للمفضلة وحذف من السلة
    const handleMoveToWishlist = async (item) => {
        try {
            await addToWishlist(item.product);
            await removeFromCart(item.product.id);
            toastManager.success('تم نقل المنتج للمفضلة');
        } catch (error) {
            toastManager.error('فشل في نقل المنتج');
        }
    };

    // مسح السلة
    const handleClearCart = () => {
        Alert.alert(
            'مسح السلة',
            'هل تريد حذف جميع المنتجات من السلة؟',
            [
                {text: 'إلغاء', style: 'cancel'},
                {
                    text: 'مسح الكل',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await clearCart();
                            toastManager.success('تم مسح السلة');
                        } catch (error) {
                            toastManager.error('فشل في مسح السلة');
                        }
                    }
                }
            ]
        );
    };

    // تطبيق كوبون الخصم
    const applyCoupon = () => {
        const validCoupons = {
            'SAVE10': 10,
            'SAVE20': 20,
            'WELCOME': 15
        };

        if (validCoupons[couponCode.toUpperCase()]) {
            setDiscount(validCoupons[couponCode.toUpperCase()]);
            setShowCouponInput(false);
            toastManager.success(`تم تطبيق خصم ${validCoupons[couponCode.toUpperCase()]}%`);
        } else {
            toastManager.error('كوبون الخصم غير صحيح');
        }
    };

    // إتمام الطلب
    const handleCheckout = async () => {
        if (cart.length === 0) {
            toastManager.warning('السلة فارغة');
            return;
        }

        setProcessingCheckout(true);
        try {
            // محاكاة معالجة الطلب
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // مسح السلة بعد النجاح
            await clearCart();
            
            toastManager.success('تم إتمام الطلب بنجاح!');
            navigation.navigate('Dashboard');
        } catch (error) {
            toastManager.error('فشل في إتمام الطلب');
        } finally {
            setProcessingCheckout(false);
        }
    };

    // عرض عنصر السلة
    const renderCartItem = ({item, index}) => (
        <Animated.View
            style={[
                styles.cartItem,
                {
                    backgroundColor: colors.surface,
                    borderColor: colors.border,
                    opacity: fadeAnim,
                    transform: [{
                        translateY: slideAnim.interpolate({
                            inputRange: [0, 50],
                            outputRange: [0, index * 10]
                        })
                    }]
                }
            ]}
        >
            {/* صورة المنتج */}
            <Image
                source={{uri: item.product.imgs[0]}}
                style={styles.productImage}
                resizeMode="cover"
            />

            {/* معلومات المنتج */}
            <View style={styles.productInfo}>
                <Text style={[styles.productName, {color: colors.text}]} numberOfLines={2}>
                    {item.product.name}
                </Text>
                
                <Text style={[styles.productPrice, {color: colors.primary[600]}]}>
                    ${item.product.price}
                </Text>

                {/* أزرار الكمية */}
                <View style={styles.quantityContainer}>
                    <TouchableOpacity
                        onPress={() => handleUpdateQuantity(item.product.id, item.quantity - 1)}
                        style={[styles.quantityButton, {backgroundColor: colors.gray[100]}]}
                        disabled={item.quantity <= 1}
                    >
                        <FontAwesomeIcon
                            icon={faMinus}
                            size={12}
                            color={item.quantity <= 1 ? colors.gray[400] : colors.gray[700]}
                        />
                    </TouchableOpacity>
                    
                    <Text style={[styles.quantityText, {color: colors.text}]}>
                        {item.quantity}
                    </Text>
                    
                    <TouchableOpacity
                        onPress={() => handleUpdateQuantity(item.product.id, item.quantity + 1)}
                        style={[styles.quantityButton, {backgroundColor: colors.gray[100]}]}
                    >
                        <FontAwesomeIcon
                            icon={faPlus}
                            size={12}
                            color={colors.gray[700]}
                        />
                    </TouchableOpacity>
                </View>
            </View>

            {/* إجمالي العنصر */}
            <View style={styles.itemActions}>
                <Text style={[styles.itemTotal, {color: colors.text}]}>
                    ${(item.product.price * item.quantity).toFixed(2)}
                </Text>
                
                <View style={styles.actionButtons}>
                    <TouchableOpacity
                        onPress={() => handleMoveToWishlist(item)}
                        style={[styles.actionButton, {backgroundColor: colors.blue[50]}]}
                    >
                        <FontAwesomeIcon
                            icon={faHeart}
                            size={14}
                            color={colors.blue[600]}
                        />
                    </TouchableOpacity>
                    
                    <TouchableOpacity
                        onPress={() => handleRemoveItem(item)}
                        style={[styles.actionButton, {backgroundColor: colors.red[50]}]}
                    >
                        <FontAwesomeIcon
                            icon={faTrash}
                            size={14}
                            color={colors.red[600]}
                        />
                    </TouchableOpacity>
                </View>
            </View>
        </Animated.View>
    );

    // عرض ملخص الطلب
    const renderOrderSummary = () => (
        <Animated.View
            style={[
                styles.summaryContainer,
                {
                    backgroundColor: colors.surface,
                    borderColor: colors.border,
                    opacity: fadeAnim,
                    transform: [{translateY: slideAnim}]
                }
            ]}
        >
            <Text style={[styles.summaryTitle, {color: colors.text}]}>
                ملخص الطلب
            </Text>

            <View style={styles.summaryRow}>
                <Text style={[styles.summaryLabel, {color: colors.gray[600]}]}>
                    المجموع الفرعي:
                </Text>
                <Text style={[styles.summaryValue, {color: colors.text}]}>
                    ${subtotal.toFixed(2)}
                </Text>
            </View>

            <View style={styles.summaryRow}>
                <Text style={[styles.summaryLabel, {color: colors.gray[600]}]}>
                    الشحن:
                </Text>
                <Text style={[styles.summaryValue, {color: shipping === 0 ? colors.green[600] : colors.text}]}>
                    {shipping === 0 ? 'مجاني' : `$${shipping.toFixed(2)}`}
                </Text>
            </View>

            <View style={styles.summaryRow}>
                <Text style={[styles.summaryLabel, {color: colors.gray[600]}]}>
                    الضريبة:
                </Text>
                <Text style={[styles.summaryValue, {color: colors.text}]}>
                    ${tax.toFixed(2)}
                </Text>
            </View>

            {discount > 0 && (
                <View style={styles.summaryRow}>
                    <Text style={[styles.summaryLabel, {color: colors.green[600]}]}>
                        الخصم ({discount}%):
                    </Text>
                    <Text style={[styles.summaryValue, {color: colors.green[600]}]}>
                        -${discountAmount.toFixed(2)}
                    </Text>
                </View>
            )}

            <View style={[styles.summaryRow, styles.totalRow, {borderTopColor: colors.border}]}>
                <Text style={[styles.totalLabel, {color: colors.text}]}>
                    الإجمالي:
                </Text>
                <Text style={[styles.totalValue, {color: colors.primary[600]}]}>
                    ${total.toFixed(2)}
                </Text>
            </View>

            {/* كوبون الخصم */}
            {!showCouponInput ? (
                <TouchableOpacity
                    onPress={() => setShowCouponInput(true)}
                    style={[styles.couponButton, {backgroundColor: colors.primary[50]}]}
                >
                    <FontAwesomeIcon
                        icon={faPercent}
                        size={16}
                        color={colors.primary[600]}
                    />
                    <Text style={[styles.couponButtonText, {color: colors.primary[600]}]}>
                        لديك كوبون خصم؟
                    </Text>
                </TouchableOpacity>
            ) : (
                <View style={styles.couponInputContainer}>
                    <TextInput
                        style={[styles.couponInput, {borderColor: colors.border, color: colors.text}]}
                        placeholder="أدخل كوبون الخصم"
                        placeholderTextColor={colors.gray[400]}
                        value={couponCode}
                        onChangeText={setCouponCode}
                        textAlign="right"
                    />
                    <ButtonImproved
                        variant="primary"
                        size="small"
                        onPress={applyCoupon}
                        style={styles.applyCouponButton}
                    >
                        تطبيق
                    </ButtonImproved>
                </View>
            )}

            {/* معلومات إضافية */}
            <View style={styles.infoContainer}>
                <View style={styles.infoItem}>
                    <FontAwesomeIcon icon={faTruck} size={16} color={colors.green[500]} />
                    <Text style={[styles.infoText, {color: colors.gray[600]}]}>
                        شحن مجاني للطلبات فوق $100
                    </Text>
                </View>
                <View style={styles.infoItem}>
                    <FontAwesomeIcon icon={faShieldAlt} size={16} color={colors.blue[500]} />
                    <Text style={[styles.infoText, {color: colors.gray[600]}]}>
                        دفع آمن ومضمون
                    </Text>
                </View>
            </View>
        </Animated.View>
    );

    // عرض السلة الفارغة
    const renderEmptyCart = () => (
        <Animated.View
            style={[
                styles.emptyContainer,
                {
                    opacity: fadeAnim,
                    transform: [{translateY: slideAnim}]
                }
            ]}
        >
            <FontAwesomeIcon
                icon={faShoppingCart}
                size={80}
                color={colors.gray[300]}
            />
            <Text style={[styles.emptyTitle, {color: colors.gray[600]}]}>
                السلة فارغة
            </Text>
            <Text style={[styles.emptySubtitle, {color: colors.gray[500]}]}>
                ابدأ بإضافة منتجات لسلة التسوق
            </Text>
            <ButtonImproved
                variant="primary"
                onPress={() => navigation.navigate('Dashboard')}
                style={styles.shopNowButton}
            >
                تسوق الآن
            </ButtonImproved>
        </Animated.View>
    );

    return (
        <SafeAreaView style={[styles.container, {backgroundColor: colors.background}]}>
            {/* Header */}
            <View style={[styles.header, {backgroundColor: colors.surface, borderBottomColor: colors.border}]}>
                <TouchableOpacity
                    onPress={() => navigation.goBack()}
                    style={styles.backButton}
                >
                    <FontAwesomeIcon
                        icon={faArrowLeft}
                        size={20}
                        color={colors.text}
                    />
                </TouchableOpacity>
                
                <Text style={[styles.headerTitle, {color: colors.text}]}>
                    سلة التسوق ({cartItemsCount})
                </Text>
                
                {cart.length > 0 && (
                    <TouchableOpacity
                        onPress={handleClearCart}
                        style={styles.clearButton}
                    >
                        <Text style={[styles.clearButtonText, {color: colors.red[600]}]}>
                            مسح الكل
                        </Text>
                    </TouchableOpacity>
                )}
            </View>

            {cart.length === 0 ? (
                renderEmptyCart()
            ) : (
                <>
                    {/* قائمة المنتجات */}
                    <FlatList
                        data={cart}
                        renderItem={renderCartItem}
                        keyExtractor={(item) => item.product.id.toString()}
                        style={styles.cartList}
                        showsVerticalScrollIndicator={false}
                        ItemSeparatorComponent={() => <View style={styles.separator} />}
                    />

                    {/* ملخص الطلب */}
                    {renderOrderSummary()}

                    {/* زر إتمام الطلب */}
                    <View style={[styles.checkoutContainer, {backgroundColor: colors.surface, borderTopColor: colors.border}]}>
                        <ButtonImproved
                            variant="primary"
                            size="large"
                            onPress={handleCheckout}
                            loading={processingCheckout}
                            style={styles.checkoutButton}
                            icon={faShoppingCart}
                        >
                            إتمام الطلب - ${total.toFixed(2)}
                        </ButtonImproved>
                    </View>
                </>
            )}

            {/* Loading Overlay */}
            <LoadingOverlay
                visible={processingCheckout}
                text="جاري معالجة الطلب..."
            />
        </SafeAreaView>
    );
});

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },

    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
    },

    backButton: {
        padding: 8,
        marginRight: 8,
    },

    headerTitle: {
        flex: 1,
        fontSize: 18,
        fontFamily: 'Cairo-SemiBold',
        textAlign: 'center',
    },

    clearButton: {
        padding: 8,
    },

    clearButtonText: {
        fontSize: 14,
        fontFamily: 'Cairo-SemiBold',
    },

    cartList: {
        flex: 1,
        padding: 16,
    },

    cartItem: {
        flexDirection: 'row',
        padding: 16,
        borderRadius: 12,
        borderWidth: 1,
        gap: 12,
    },

    separator: {
        height: 12,
    },

    productImage: {
        width: 80,
        height: 80,
        borderRadius: 8,
    },

    productInfo: {
        flex: 1,
        gap: 8,
    },

    productName: {
        fontSize: 16,
        fontFamily: 'Cairo-SemiBold',
        textAlign: 'right',
    },

    productPrice: {
        fontSize: 14,
        fontFamily: 'Cairo-SemiBold',
    },

    quantityContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'flex-end',
        gap: 8,
    },

    quantityButton: {
        width: 32,
        height: 32,
        borderRadius: 16,
        alignItems: 'center',
        justifyContent: 'center',
    },

    quantityText: {
        fontSize: 16,
        fontFamily: 'Cairo-SemiBold',
        minWidth: 30,
        textAlign: 'center',
    },

    itemActions: {
        alignItems: 'flex-end',
        justifyContent: 'space-between',
    },

    itemTotal: {
        fontSize: 16,
        fontFamily: 'Cairo-Bold',
    },

    actionButtons: {
        flexDirection: 'row',
        gap: 8,
    },

    actionButton: {
        width: 32,
        height: 32,
        borderRadius: 16,
        alignItems: 'center',
        justifyContent: 'center',
    },

    summaryContainer: {
        margin: 16,
        padding: 20,
        borderRadius: 12,
        borderWidth: 1,
    },

    summaryTitle: {
        fontSize: 18,
        fontFamily: 'Cairo-Bold',
        textAlign: 'right',
        marginBottom: 16,
    },

    summaryRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },

    summaryLabel: {
        fontSize: 14,
        fontFamily: 'Cairo-Regular',
    },

    summaryValue: {
        fontSize: 14,
        fontFamily: 'Cairo-SemiBold',
    },

    totalRow: {
        borderTopWidth: 1,
        paddingTop: 12,
        marginTop: 8,
    },

    totalLabel: {
        fontSize: 16,
        fontFamily: 'Cairo-Bold',
    },

    totalValue: {
        fontSize: 18,
        fontFamily: 'Cairo-Bold',
    },

    couponButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 12,
        borderRadius: 8,
        marginTop: 12,
        gap: 8,
    },

    couponButtonText: {
        fontSize: 14,
        fontFamily: 'Cairo-SemiBold',
    },

    couponInputContainer: {
        flexDirection: 'row',
        marginTop: 12,
        gap: 8,
    },

    couponInput: {
        flex: 1,
        borderWidth: 1,
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 8,
        fontSize: 14,
        fontFamily: 'Cairo-Regular',
    },

    applyCouponButton: {
        minWidth: 80,
    },

    infoContainer: {
        marginTop: 16,
        gap: 8,
    },

    infoItem: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },

    infoText: {
        fontSize: 12,
        fontFamily: 'Cairo-Regular',
    },

    checkoutContainer: {
        padding: 16,
        borderTopWidth: 1,
    },

    checkoutButton: {
        width: '100%',
    },

    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 40,
        gap: 16,
    },

    emptyTitle: {
        fontSize: 24,
        fontFamily: 'Cairo-Bold',
        textAlign: 'center',
    },

    emptySubtitle: {
        fontSize: 16,
        fontFamily: 'Cairo-Regular',
        textAlign: 'center',
        lineHeight: 24,
    },

    shopNowButton: {
        marginTop: 16,
        minWidth: 150,
    },
});

export default CartImproved;
