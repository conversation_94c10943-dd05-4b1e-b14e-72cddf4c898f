import React, {useState, useEffect, useCallback, useRef} from 'react';
import {
    View,
    ScrollView,
    Text,
    TextInput,
    TouchableOpacity,
    BackHandler,
    StyleSheet,
    FlatList,
    Animated,
    Keyboard
} from 'react-native';

import {ProductGridImproved} from '../components/ProductGridImproved';
import {CategoriesImproved} from '../components/CategoriesImproved';
import {Card, Button, Badge, LoadingSpinner} from '../components/ui';

import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
    faSearch, 
    faTimes, 
    faFilter, 
    faSort, 
    faMicrophone,
    faBarcode,
    faHistory,
    faTrendingUp,
    faArrowLeft
} from '@fortawesome/free-solid-svg-icons';

import {theme} from '../styles/theme';

const SearchImproved = ({navigation}) => {
    // حالات البحث
    const [searchText, setSearchText] = useState('');
    const [searched, setSearched] = useState(false);
    const [isSearching, setIsSearching] = useState(false);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [suggestions, setSuggestions] = useState([]);
    const [loading, setLoading] = useState(false);
    
    // حالات الفلاتر والترتيب
    const [showFilters, setShowFilters] = useState(false);
    const [sortBy, setSortBy] = useState('relevance');
    const [filters, setFilters] = useState({
        priceRange: [0, 1000],
        brands: [],
        rating: 0,
        inStock: false
    });
    
    // البيانات
    const [recentSearches, setRecentSearches] = useState([
        'قطع غيار BMW',
        'فلاتر الهواء',
        'زيوت المحرك',
        'إطارات ميشلان',
        'بطاريات السيارة'
    ]);
    
    const [trendingSearches] = useState([
        'فرامل',
        'زيوت',
        'فلاتر',
        'إطارات',
        'بطاريات',
        'مصابيح LED',
        'قطع غيار تويوتا',
        'زيت محرك 5W-30'
    ]);
    
    const [searchResults, setSearchResults] = useState([]);
    const [resultCount, setResultCount] = useState(0);
    
    // المراجع والرسوم المتحركة
    const searchInputRef = useRef(null);
    const suggestionAnimation = useRef(new Animated.Value(0)).current;
    const filterAnimation = useRef(new Animated.Value(0)).current;
    
    // معالج زر الرجوع
    const handleBackButtonClick = useCallback(() => {
        if (showFilters) {
            setShowFilters(false);
            return true;
        }
        if (searched) {
            setSearched(false);
            setSearchText('');
            setShowSuggestions(false);
            return true;
        }
        return false;
    }, [searched, showFilters]);
    
    useEffect(() => {
        BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
        return () => {
            BackHandler.removeEventListener('hardwareBackPress', handleBackButtonClick);
        };
    }, [handleBackButtonClick]);
    
    // البحث التلقائي أثناء الكتابة
    useEffect(() => {
        const timeoutId = setTimeout(() => {
            if (searchText.length > 2 && !searched) {
                fetchSuggestions(searchText);
            } else {
                setSuggestions([]);
                setShowSuggestions(false);
            }
        }, 300);
        
        return () => clearTimeout(timeoutId);
    }, [searchText, searched]);
    
    // جلب الاقتراحات
    const fetchSuggestions = async (query) => {
        try {
            setLoading(true);
            // محاكاة API call
            await new Promise(resolve => setTimeout(resolve, 200));
            
            const mockSuggestions = [
                `${query} BMW`,
                `${query} تويوتا`,
                `${query} هيونداي`,
                `${query} أصلي`,
                `${query} متوافق`
            ].filter(s => s.toLowerCase().includes(query.toLowerCase()));
            
            setSuggestions(mockSuggestions);
            setShowSuggestions(true);
            
            Animated.timing(suggestionAnimation, {
                toValue: 1,
                duration: 200,
                useNativeDriver: false,
            }).start();
        } catch (error) {
            console.error('Error fetching suggestions:', error);
        } finally {
            setLoading(false);
        }
    };
    
    // تنفيذ البحث
    const handleSearch = useCallback(async (query = searchText) => {
        if (query.trim().length === 0) return;
        
        setIsSearching(true);
        setSearched(true);
        setShowSuggestions(false);
        Keyboard.dismiss();
        
        // إضافة للبحث الأخير
        if (!recentSearches.includes(query.trim())) {
            setRecentSearches(prev => [query.trim(), ...prev.slice(0, 4)]);
        }
        
        try {
            // محاكاة API call
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // محاكاة النتائج
            const mockResults = Array.from({length: 15}, (_, i) => ({
                id: i + 1,
                name: `منتج ${query} ${i + 1}`,
                price: Math.floor(Math.random() * 500) + 50,
                rating: Math.random() * 5,
                image: `https://via.placeholder.com/150?text=Product${i + 1}`
            }));
            
            setSearchResults(mockResults);
            setResultCount(mockResults.length);
        } catch (error) {
            console.error('Search error:', error);
        } finally {
            setIsSearching(false);
        }
    }, [searchText, recentSearches]);
    
    // مسح البحث
    const clearSearch = () => {
        setSearchText('');
        setSearched(false);
        setShowSuggestions(false);
        setSearchResults([]);
        setResultCount(0);
        searchInputRef.current?.focus();
    };
    
    // معالج الاقتراح
    const handleSuggestionPress = (suggestion) => {
        setSearchText(suggestion);
        handleSearch(suggestion);
    };
    
    // معالج البحث الأخير
    const handleRecentSearch = (text) => {
        setSearchText(text);
        handleSearch(text);
    };
    
    // تبديل الفلاتر
    const toggleFilters = () => {
        setShowFilters(!showFilters);
        Animated.timing(filterAnimation, {
            toValue: showFilters ? 0 : 1,
            duration: 300,
            useNativeDriver: false,
        }).start();
    };
    
    // عرض شريط البحث
    const renderSearchBar = () => (
        <Card style={styles.searchCard} padding="small">
            <View style={styles.searchContainer}>
                {searched && (
                    <TouchableOpacity 
                        onPress={() => navigation.goBack()}
                        style={styles.backButton}
                    >
                        <FontAwesomeIcon 
                            icon={faArrowLeft} 
                            size={18} 
                            color={theme.colors.gray[600]} 
                        />
                    </TouchableOpacity>
                )}
                
                <View style={styles.searchInputContainer}>
                    <FontAwesomeIcon 
                        icon={faSearch} 
                        size={16} 
                        color={theme.colors.gray[400]} 
                    />
                    
                    <TextInput
                        ref={searchInputRef}
                        value={searchText}
                        onChangeText={setSearchText}
                        style={styles.searchInput}
                        placeholder="ابحث عن قطع الغيار..."
                        placeholderTextColor={theme.colors.gray[400]}
                        onSubmitEditing={() => handleSearch()}
                        returnKeyType="search"
                        autoFocus={!searched}
                        textAlign="right"
                    />
                    
                    {searchText.length > 0 && (
                        <TouchableOpacity onPress={clearSearch} style={styles.clearButton}>
                            <FontAwesomeIcon 
                                icon={faTimes} 
                                size={14} 
                                color={theme.colors.gray[400]} 
                            />
                        </TouchableOpacity>
                    )}
                </View>
                
                <View style={styles.searchActions}>
                    <TouchableOpacity style={styles.actionButton}>
                        <FontAwesomeIcon 
                            icon={faMicrophone} 
                            size={16} 
                            color={theme.colors.primary[600]} 
                        />
                    </TouchableOpacity>
                    
                    <TouchableOpacity style={styles.actionButton}>
                        <FontAwesomeIcon 
                            icon={faBarcode} 
                            size={16} 
                            color={theme.colors.primary[600]} 
                        />
                    </TouchableOpacity>
                </View>
            </View>
        </Card>
    );
    
    // عرض الاقتراحات
    const renderSuggestions = () => {
        if (!showSuggestions || suggestions.length === 0) return null;
        
        return (
            <Animated.View 
                style={[
                    styles.suggestionsContainer,
                    {
                        opacity: suggestionAnimation,
                        transform: [{
                            translateY: suggestionAnimation.interpolate({
                                inputRange: [0, 1],
                                outputRange: [-10, 0]
                            })
                        }]
                    }
                ]}
            >
                <Card style={styles.suggestionsCard}>
                    {suggestions.map((suggestion, index) => (
                        <TouchableOpacity
                            key={index}
                            style={styles.suggestionItem}
                            onPress={() => handleSuggestionPress(suggestion)}
                        >
                            <FontAwesomeIcon 
                                icon={faSearch} 
                                size={14} 
                                color={theme.colors.gray[400]} 
                            />
                            <Text style={styles.suggestionText}>{suggestion}</Text>
                        </TouchableOpacity>
                    ))}
                </Card>
            </Animated.View>
        );
    };
    
    // عرض شريط النتائج والفلاتر
    const renderResultsHeader = () => {
        if (!searched) return null;
        
        return (
            <View style={styles.resultsHeader}>
                <View style={styles.resultsInfo}>
                    <Text style={styles.resultsCount}>
                        {isSearching ? 'جاري البحث...' : `${resultCount} نتيجة`}
                    </Text>
                    <Text style={styles.searchQuery}>
                        نتائج البحث عن "{searchText}"
                    </Text>
                </View>
                
                <View style={styles.resultsActions}>
                    <TouchableOpacity 
                        style={styles.filterButton}
                        onPress={toggleFilters}
                    >
                        <FontAwesomeIcon 
                            icon={faFilter} 
                            size={16} 
                            color={theme.colors.primary[600]} 
                        />
                        <Text style={styles.filterButtonText}>فلتر</Text>
                    </TouchableOpacity>
                    
                    <TouchableOpacity style={styles.sortButton}>
                        <FontAwesomeIcon 
                            icon={faSort} 
                            size={16} 
                            color={theme.colors.primary[600]} 
                        />
                        <Text style={styles.sortButtonText}>ترتيب</Text>
                    </TouchableOpacity>
                </View>
            </View>
        );
    };
    
    // عرض الفلاتر
    const renderFilters = () => {
        if (!showFilters) return null;

        return (
            <Animated.View
                style={[
                    styles.filtersContainer,
                    {
                        opacity: filterAnimation,
                        transform: [{
                            translateY: filterAnimation.interpolate({
                                inputRange: [0, 1],
                                outputRange: [-20, 0]
                            })
                        }]
                    }
                ]}
            >
                <Card style={styles.filtersCard}>
                    <View style={styles.filtersHeader}>
                        <Text style={styles.filtersTitle}>الفلاتر</Text>
                        <TouchableOpacity onPress={toggleFilters}>
                            <FontAwesomeIcon
                                icon={faTimes}
                                size={16}
                                color={theme.colors.gray[600]}
                            />
                        </TouchableOpacity>
                    </View>

                    {/* فلتر السعر */}
                    <View style={styles.filterSection}>
                        <Text style={styles.filterLabel}>نطاق السعر</Text>
                        <View style={styles.priceRange}>
                            <Text style={styles.priceText}>${filters.priceRange[0]}</Text>
                            <Text style={styles.priceText}>-</Text>
                            <Text style={styles.priceText}>${filters.priceRange[1]}</Text>
                        </View>
                    </View>

                    {/* فلتر العلامات التجارية */}
                    <View style={styles.filterSection}>
                        <Text style={styles.filterLabel}>العلامة التجارية</Text>
                        <View style={styles.brandsContainer}>
                            {['BMW', 'تويوتا', 'هيونداي', 'نيسان', 'كيا'].map((brand, index) => (
                                <TouchableOpacity
                                    key={index}
                                    style={[
                                        styles.brandTag,
                                        filters.brands.includes(brand) && styles.brandTagSelected
                                    ]}
                                >
                                    <Text style={[
                                        styles.brandText,
                                        filters.brands.includes(brand) && styles.brandTextSelected
                                    ]}>
                                        {brand}
                                    </Text>
                                </TouchableOpacity>
                            ))}
                        </View>
                    </View>

                    {/* فلتر التقييم */}
                    <View style={styles.filterSection}>
                        <Text style={styles.filterLabel}>التقييم</Text>
                        <View style={styles.ratingFilter}>
                            {[5, 4, 3, 2, 1].map((rating) => (
                                <TouchableOpacity
                                    key={rating}
                                    style={styles.ratingOption}
                                >
                                    <Text style={styles.ratingText}>{rating} نجوم فأكثر</Text>
                                </TouchableOpacity>
                            ))}
                        </View>
                    </View>

                    <View style={styles.filtersActions}>
                        <Button variant="outline" style={styles.clearFiltersButton}>
                            مسح الفلاتر
                        </Button>
                        <Button variant="primary" style={styles.applyFiltersButton}>
                            تطبيق الفلاتر
                        </Button>
                    </View>
                </Card>
            </Animated.View>
        );
    };

    // عرض المحتوى الرئيسي
    const renderMainContent = () => {
        if (searched) {
            return (
                <View style={styles.searchResults}>
                    {isSearching ? (
                        <LoadingSpinner size="large" />
                    ) : (
                        <ProductGridImproved
                            navigation={navigation}
                            searchText={searchText}
                            results={searchResults}
                        />
                    )}
                </View>
            );
        }

        return (
            <ScrollView style={styles.exploreContent}>
                {/* الفئات */}
                <View style={styles.section}>
                    <Text style={styles.sectionTitle}>تصفح الفئات</Text>
                    <CategoriesImproved />
                </View>

                {/* البحث الأخير */}
                {recentSearches.length > 0 && (
                    <View style={styles.section}>
                        <Text style={styles.sectionTitle}>عمليات البحث الأخيرة</Text>
                        {recentSearches.map((item, index) => (
                            <TouchableOpacity
                                key={index}
                                style={styles.recentSearchItem}
                                onPress={() => handleRecentSearch(item)}
                            >
                                <FontAwesomeIcon
                                    icon={faHistory}
                                    size={14}
                                    color={theme.colors.gray[400]}
                                />
                                <Text style={styles.recentSearchText}>{item}</Text>
                            </TouchableOpacity>
                        ))}
                    </View>
                )}

                {/* البحث الشائع */}
                <View style={styles.section}>
                    <Text style={styles.sectionTitle}>البحث الشائع</Text>
                    <View style={styles.trendingContainer}>
                        {trendingSearches.map((item, index) => (
                            <TouchableOpacity
                                key={index}
                                style={styles.trendingTag}
                                onPress={() => handleRecentSearch(item)}
                            >
                                <FontAwesomeIcon
                                    icon={faTrendingUp}
                                    size={12}
                                    color={theme.colors.primary[600]}
                                />
                                <Text style={styles.trendingText}>{item}</Text>
                            </TouchableOpacity>
                        ))}
                    </View>
                </View>
            </ScrollView>
        );
    };
    
    return (
        <View style={styles.container}>
            {/* شريط البحث */}
            <View style={styles.searchHeader}>
                {renderSearchBar()}
            </View>

            {/* الاقتراحات */}
            {renderSuggestions()}

            {/* شريط النتائج والفلاتر */}
            {renderResultsHeader()}

            {/* الفلاتر */}
            {renderFilters()}

            {/* المحتوى الرئيسي */}
            {renderMainContent()}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.gray[50],
    },
    
    searchHeader: {
        backgroundColor: '#fff',
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.gray[200],
    },
    
    searchCard: {
        backgroundColor: theme.colors.gray[50],
    },
    
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: theme.spacing.sm,
    },
    
    backButton: {
        padding: theme.spacing.xs,
    },
    
    searchInputContainer: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#fff',
        borderRadius: theme.borderRadius.lg,
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        gap: theme.spacing.sm,
    },
    
    searchInput: {
        flex: 1,
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.gray[900],
        textAlign: 'right',
    },
    
    clearButton: {
        padding: theme.spacing.xs,
    },
    
    searchActions: {
        flexDirection: 'row',
        gap: theme.spacing.xs,
    },
    
    actionButton: {
        padding: theme.spacing.sm,
        backgroundColor: theme.colors.primary[50],
        borderRadius: theme.borderRadius.md,
    },
    
    suggestionsContainer: {
        position: 'absolute',
        top: 80,
        left: theme.spacing.md,
        right: theme.spacing.md,
        zIndex: 1000,
    },
    
    suggestionsCard: {
        backgroundColor: '#fff',
        maxHeight: 200,
    },
    
    suggestionItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: theme.spacing.sm,
        paddingHorizontal: theme.spacing.md,
        gap: theme.spacing.sm,
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.gray[100],
    },
    
    suggestionText: {
        fontSize: theme.typography.fontSize.md,
        color: theme.colors.gray[700],
        fontFamily: theme.typography.fontFamily.primary,
    },
    
    resultsHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        backgroundColor: '#fff',
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.gray[200],
    },
    
    resultsInfo: {
        flex: 1,
    },
    
    resultsCount: {
        fontSize: theme.typography.fontSize.sm,
        color: theme.colors.gray[600],
        fontFamily: theme.typography.fontFamily.primary,
    },
    
    searchQuery: {
        fontSize: theme.typography.fontSize.md,
        color: theme.colors.gray[900],
        fontFamily: theme.typography.fontFamily.primarySemiBold,
    },
    
    resultsActions: {
        flexDirection: 'row',
        gap: theme.spacing.sm,
    },
    
    filterButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        backgroundColor: theme.colors.primary[50],
        borderRadius: theme.borderRadius.md,
        gap: theme.spacing.xs,
    },
    
    filterButtonText: {
        fontSize: theme.typography.fontSize.sm,
        color: theme.colors.primary[600],
        fontFamily: theme.typography.fontFamily.primarySemiBold,
    },
    
    sortButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        backgroundColor: theme.colors.gray[100],
        borderRadius: theme.borderRadius.md,
        gap: theme.spacing.xs,
    },
    
    sortButtonText: {
        fontSize: theme.typography.fontSize.sm,
        color: theme.colors.gray[600],
        fontFamily: theme.typography.fontFamily.primarySemiBold,
    },
    
    searchResults: {
        flex: 1,
        padding: theme.spacing.md,
    },
    
    exploreContent: {
        flex: 1,
    },
    
    section: {
        marginBottom: theme.spacing.xl,
    },
    
    sectionTitle: {
        fontSize: theme.typography.fontSize.lg,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.gray[900],
        marginHorizontal: theme.spacing.md,
        marginBottom: theme.spacing.md,
    },
    
    recentSearchItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: theme.spacing.sm,
        paddingHorizontal: theme.spacing.md,
        gap: theme.spacing.sm,
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.gray[100],
    },
    
    recentSearchText: {
        fontSize: theme.typography.fontSize.md,
        color: theme.colors.gray[700],
        fontFamily: theme.typography.fontFamily.primary,
    },
    
    trendingContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        paddingHorizontal: theme.spacing.md,
        gap: theme.spacing.sm,
    },
    
    trendingTag: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        backgroundColor: theme.colors.primary[50],
        borderRadius: theme.borderRadius.full,
        gap: theme.spacing.xs,
    },
    
    trendingText: {
        fontSize: theme.typography.fontSize.sm,
        color: theme.colors.primary[600],
        fontFamily: theme.typography.fontFamily.primary,
    },

    // أنماط الفلاتر
    filtersContainer: {
        position: 'absolute',
        top: 140,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        zIndex: 1000,
    },

    filtersCard: {
        margin: theme.spacing.md,
        maxHeight: '80%',
        backgroundColor: '#fff',
    },

    filtersHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.md,
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.gray[200],
    },

    filtersTitle: {
        fontSize: theme.typography.fontSize.lg,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.gray[900],
    },

    filterSection: {
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.md,
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.gray[100],
    },

    filterLabel: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primarySemiBold,
        color: theme.colors.gray[900],
        marginBottom: theme.spacing.sm,
    },

    priceRange: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        gap: theme.spacing.md,
    },

    priceText: {
        fontSize: theme.typography.fontSize.md,
        color: theme.colors.primary[600],
        fontFamily: theme.typography.fontFamily.primarySemiBold,
    },

    brandsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: theme.spacing.sm,
    },

    brandTag: {
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        backgroundColor: theme.colors.gray[100],
        borderRadius: theme.borderRadius.full,
        borderWidth: 1,
        borderColor: theme.colors.gray[200],
    },

    brandTagSelected: {
        backgroundColor: theme.colors.primary[600],
        borderColor: theme.colors.primary[600],
    },

    brandText: {
        fontSize: theme.typography.fontSize.sm,
        color: theme.colors.gray[700],
        fontFamily: theme.typography.fontFamily.primary,
    },

    brandTextSelected: {
        color: '#fff',
    },

    ratingFilter: {
        gap: theme.spacing.sm,
    },

    ratingOption: {
        paddingVertical: theme.spacing.sm,
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.gray[100],
    },

    ratingText: {
        fontSize: theme.typography.fontSize.md,
        color: theme.colors.gray[700],
        fontFamily: theme.typography.fontFamily.primary,
    },

    filtersActions: {
        flexDirection: 'row',
        gap: theme.spacing.sm,
        padding: theme.spacing.md,
    },

    clearFiltersButton: {
        flex: 1,
    },

    applyFiltersButton: {
        flex: 1,
    },
});

export default SearchImproved;
