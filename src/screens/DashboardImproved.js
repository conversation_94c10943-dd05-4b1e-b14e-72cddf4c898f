import React, {useEffect, useState, useCallback, useRef} from 'react';
import {
    View,
    ScrollView,
    Text,
    TextInput,
    TouchableOpacity,
    BackHandler,
    SafeAreaView,
    RefreshControl,
    StyleSheet,
    Animated,
    Dimensions
} from 'react-native';

// المكونات المحسنة
import {useTheme} from '../components/ThemeProvider';
import {ProductStoreImproved} from '../store/productImproved';
import {toastManager} from '../components/ToastManager';
import {
    LoadingSpinner,
    SkeletonProductGrid,
    SkeletonCategories,
    LoadingOverlay
} from '../components/LoadingStates';

// المكونات الأصلية المحسنة
import {Header} from '../components/Header';
import {CategoriesImproved} from '../components/CategoriesImproved';
import {ProductGridImproved} from '../components/ProductGridImproved';
import ButtonImproved from '../components/ui/ButtonImproved';

// المكونات الأصلية (للتوافق)
import {ProductCarousel} from '../components/ProductCarousel';
import {ProductSlider} from '../components/ProductSlider';

import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
    faSearch, 
    faTimes, 
    faFilter, 
    faRefresh,
    faExclamationTriangle,
    faWifi
} from '@fortawesome/free-solid-svg-icons';

import {observer} from 'mobx-react';

const {width} = Dimensions.get('window');

const DashboardImproved = observer(({navigation}) => {
    // Theme
    const {colors, typography, spacing} = useTheme();
    
    // حالات البحث والتحديث
    const [searching, setSearching] = useState(false);
    const [searchText, setSearchText] = useState('');
    const [searched, setSearched] = useState(false);
    const [refreshing, setRefreshing] = useState(false);
    const [showFilters, setShowFilters] = useState(false);
    const [initialLoading, setInitialLoading] = useState(true);

    // Animation للبحث
    const searchAnimation = useRef(new Animated.Value(0)).current;
    const filterAnimation = useRef(new Animated.Value(0)).current;

    // Store state
    const {
        state: {categories, products, errors, lastError},
        loadingManager,
        getCategories,
        getProducts,
        getSearchedProducts,
        clearErrors,
        refreshData
    } = ProductStoreImproved;

    // معالج زر الرجوع
    const handleBackButtonClick = useCallback(() => {
        if (searched) {
            handleClearSearch();
            return true;
        }
        if (showFilters) {
            setShowFilters(false);
            return true;
        }
        return false;
    }, [searched, showFilters]);

    // تحميل البيانات الأولية
    useEffect(() => {
        initializeData();
    }, []);

    // معالج زر الرجوع
    useEffect(() => {
        BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
        return () => {
            BackHandler.removeEventListener('hardwareBackPress', handleBackButtonClick);
        };
    }, [handleBackButtonClick]);

    // تحميل البيانات الأولية
    const initializeData = async () => {
        try {
            setInitialLoading(true);
            clearErrors();

            // تحميل الفئات والمنتجات بالتوازي
            const [categoriesResult, productsResult] = await Promise.all([
                getCategories(true, false),
                getProducts({}, true, false)
            ]);

            if (!categoriesResult.success && !categoriesResult.fallbackUsed) {
                toastManager.warning('تم استخدام البيانات المحفوظة للفئات');
            }

            if (!productsResult.success && !productsResult.fallbackUsed) {
                toastManager.warning('تم استخدام البيانات المحفوظة للمنتجات');
            }

            if (categoriesResult.success && productsResult.success) {
                toastManager.success('تم تحميل البيانات بنجاح');
            }

        } catch (error) {
            console.error('Error initializing data:', error);
            toastManager.error('فشل في تحميل البيانات الأولية');
        } finally {
            setInitialLoading(false);
        }
    };

    // معالج التحديث
    const onRefresh = useCallback(async () => {
        setRefreshing(true);
        try {
            await refreshData();
            toastManager.success('تم تحديث البيانات');
        } catch (error) {
            toastManager.error('فشل في تحديث البيانات');
        } finally {
            setRefreshing(false);
        }
    }, []);

    // معالج البحث
    const handleSearch = useCallback(async () => {
        if (searchText.trim().length === 0) {
            toastManager.warning('يرجى إدخال نص للبحث');
            return;
        }

        setSearching(true);
        setSearched(true);

        // Animation للبحث
        Animated.timing(searchAnimation, {
            toValue: 1,
            duration: 300,
            useNativeDriver: false,
        }).start();

        try {
            const result = await getSearchedProducts(searchText.trim());
            
            if (result.success) {
                if (result.data.length === 0) {
                    toastManager.info('لم يتم العثور على نتائج');
                } else {
                    toastManager.success(`تم العثور على ${result.data.length} منتج`);
                }
            } else {
                toastManager.error('فشل في البحث');
            }
        } catch (error) {
            toastManager.error('حدث خطأ أثناء البحث');
        } finally {
            setSearching(false);
        }
    }, [searchText]);

    // مسح البحث
    const handleClearSearch = useCallback(() => {
        setSearchText('');
        setSearched(false);
        setSearching(false);

        Animated.timing(searchAnimation, {
            toValue: 0,
            duration: 300,
            useNativeDriver: false,
        }).start();
    }, []);

    // تبديل الفلاتر
    const toggleFilters = useCallback(() => {
        setShowFilters(!showFilters);
        
        Animated.timing(filterAnimation, {
            toValue: showFilters ? 0 : 1,
            duration: 300,
            useNativeDriver: false,
        }).start();
    }, [showFilters]);

    // عرض شريط البحث
    const renderSearchBar = () => (
        <View style={[styles.searchContainer, {backgroundColor: colors.surface}]}>
            <View style={[styles.searchInputContainer, {borderColor: colors.border}]}>
                <FontAwesomeIcon
                    icon={faSearch}
                    size={16}
                    color={colors.gray[400]}
                    style={styles.searchIcon}
                />
                
                <TextInput
                    style={[styles.searchInput, {color: colors.text}]}
                    placeholder="ابحث عن قطع الغيار..."
                    placeholderTextColor={colors.gray[400]}
                    value={searchText}
                    onChangeText={setSearchText}
                    onSubmitEditing={handleSearch}
                    returnKeyType="search"
                    textAlign="right"
                />
                
                {searchText.length > 0 && (
                    <TouchableOpacity
                        onPress={() => setSearchText('')}
                        style={styles.clearButton}
                    >
                        <FontAwesomeIcon
                            icon={faTimes}
                            size={14}
                            color={colors.gray[400]}
                        />
                    </TouchableOpacity>
                )}
            </View>

            <ButtonImproved
                variant="primary"
                size="medium"
                onPress={handleSearch}
                loading={searching}
                disabled={searchText.trim().length === 0}
                style={styles.searchButton}
            >
                بحث
            </ButtonImproved>
        </View>
    );

    // عرض شريط النتائج
    const renderResultsBar = () => {
        if (!searched) return null;

        return (
            <Animated.View
                style={[
                    styles.resultsBar,
                    {
                        backgroundColor: colors.surface,
                        borderBottomColor: colors.border,
                        opacity: searchAnimation,
                        transform: [{
                            translateY: searchAnimation.interpolate({
                                inputRange: [0, 1],
                                outputRange: [-50, 0]
                            })
                        }]
                    }
                ]}
            >
                <Text style={[styles.resultsText, {color: colors.text}]}>
                    نتائج البحث عن "{searchText}"
                </Text>
                
                <View style={styles.resultsActions}>
                    <TouchableOpacity
                        onPress={toggleFilters}
                        style={[styles.filterButton, {backgroundColor: colors.primary[50]}]}
                    >
                        <FontAwesomeIcon
                            icon={faFilter}
                            size={14}
                            color={colors.primary[600]}
                        />
                        <Text style={[styles.filterButtonText, {color: colors.primary[600]}]}>
                            فلتر
                        </Text>
                    </TouchableOpacity>
                    
                    <TouchableOpacity
                        onPress={handleClearSearch}
                        style={styles.clearSearchButton}
                    >
                        <FontAwesomeIcon
                            icon={faTimes}
                            size={14}
                            color={colors.gray[500]}
                        />
                        <Text style={[styles.clearSearchText, {color: colors.gray[500]}]}>
                            مسح
                        </Text>
                    </TouchableOpacity>
                </View>
            </Animated.View>
        );
    };

    // عرض رسالة الخطأ
    const renderErrorMessage = () => {
        if (!lastError) return null;

        return (
            <View style={[styles.errorContainer, {backgroundColor: colors.error[50]}]}>
                <FontAwesomeIcon
                    icon={faExclamationTriangle}
                    size={20}
                    color={colors.error[500]}
                />
                <Text style={[styles.errorText, {color: colors.error[700]}]}>
                    {lastError.message}
                </Text>
                <TouchableOpacity
                    onPress={() => clearErrors()}
                    style={styles.errorCloseButton}
                >
                    <FontAwesomeIcon
                        icon={faTimes}
                        size={16}
                        color={colors.error[500]}
                    />
                </TouchableOpacity>
            </View>
        );
    };

    // عرض المحتوى الرئيسي
    const renderMainContent = () => {
        if (initialLoading) {
            return (
                <View style={styles.loadingContainer}>
                    <SkeletonCategories count={5} />
                    <SkeletonProductGrid itemsPerRow={2} rows={3} />
                </View>
            );
        }

        if (searched) {
            return (
                <ProductGridImproved
                    navigation={navigation}
                    searchText={searchText}
                    byCategory={false}
                />
            );
        }

        return (
            <>
                {/* الفئات */}
                <View style={styles.section}>
                    <Text style={[styles.sectionTitle, {color: colors.text}]}>
                        الفئات
                    </Text>
                    {loadingManager.getLoadingState('getCategories') ? (
                        <SkeletonCategories count={5} />
                    ) : (
                        <CategoriesImproved />
                    )}
                </View>

                {/* المنتجات المميزة */}
                <View style={styles.section}>
                    <Text style={[styles.sectionTitle, {color: colors.text}]}>
                        المنتجات المميزة
                    </Text>
                    <ProductCarousel navigation={navigation} />
                </View>

                {/* أحدث المنتجات */}
                <View style={styles.section}>
                    <Text style={[styles.sectionTitle, {color: colors.text}]}>
                        أحدث المنتجات
                    </Text>
                    <ProductSlider navigation={navigation} />
                </View>

                {/* جميع المنتجات */}
                <View style={styles.section}>
                    <Text style={[styles.sectionTitle, {color: colors.text}]}>
                        جميع المنتجات
                    </Text>
                    {loadingManager.getLoadingState('getProducts') ? (
                        <SkeletonProductGrid itemsPerRow={2} rows={2} />
                    ) : (
                        <ProductGridImproved
                            navigation={navigation}
                            searchText=""
                            byCategory={true}
                        />
                    )}
                </View>
            </>
        );
    };

    return (
        <SafeAreaView style={[styles.container, {backgroundColor: colors.background}]}>
            {/* Header */}
            <Header navigation={navigation} />

            {/* شريط البحث */}
            {renderSearchBar()}

            {/* رسالة الخطأ */}
            {renderErrorMessage()}

            {/* شريط النتائج */}
            {renderResultsBar()}

            {/* المحتوى الرئيسي */}
            <ScrollView
                style={styles.scrollView}
                showsVerticalScrollIndicator={false}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={onRefresh}
                        colors={[colors.primary[600]]}
                        tintColor={colors.primary[600]}
                    />
                }
            >
                {renderMainContent()}
            </ScrollView>

            {/* Loading Overlay للعمليات الطويلة */}
            <LoadingOverlay
                visible={loadingManager.getLoadingState('refreshData')}
                text="جاري تحديث البيانات..."
            />
        </SafeAreaView>
    );
});

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },

    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        gap: 12,
        borderBottomWidth: 1,
    },

    searchInputContainer: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 8,
        backgroundColor: '#fff',
    },

    searchIcon: {
        marginRight: 8,
    },

    searchInput: {
        flex: 1,
        fontSize: 16,
        fontFamily: 'Cairo-Regular',
        textAlign: 'right',
    },

    clearButton: {
        padding: 4,
        marginLeft: 8,
    },

    searchButton: {
        minWidth: 80,
    },

    resultsBar: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
    },

    resultsText: {
        fontSize: 16,
        fontFamily: 'Cairo-SemiBold',
    },

    resultsActions: {
        flexDirection: 'row',
        gap: 12,
    },

    filterButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 6,
        gap: 4,
    },

    filterButtonText: {
        fontSize: 14,
        fontFamily: 'Cairo-SemiBold',
    },

    clearSearchButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        paddingVertical: 6,
        gap: 4,
    },

    clearSearchText: {
        fontSize: 14,
        fontFamily: 'Cairo-Regular',
    },

    errorContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        gap: 12,
    },

    errorText: {
        flex: 1,
        fontSize: 14,
        fontFamily: 'Cairo-Regular',
        textAlign: 'right',
    },

    errorCloseButton: {
        padding: 4,
    },

    scrollView: {
        flex: 1,
    },

    loadingContainer: {
        padding: 16,
    },

    section: {
        marginBottom: 24,
    },

    sectionTitle: {
        fontSize: 18,
        fontFamily: 'Cairo-Bold',
        marginHorizontal: 16,
        marginBottom: 12,
        textAlign: 'right',
    },
});

export default DashboardImproved;
