import React, {useEffect, useState, useRef} from 'react';
import {
    ScrollView,
    Image,
    Text,
    View,
    Dimensions,
    TouchableOpacity,
    SafeAreaView,
    StyleSheet,
    StatusBar,
    Animated,
    Share,
    Alert
} from 'react-native';
import Carousel, {Pagination} from 'react-native-snap-carousel-v4';

import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faHeart} from '@fortawesome/free-regular-svg-icons';
import {
    faHeart as faHeartFilled,
    faCartShopping,
    faArrowLeft,
    faShare,
    faStar,
    faStarHalfAlt,
    faPlus,
    faMinus,
    faCheck,
    faExclamationTriangle,
    faShieldAlt,
    faTruck,
    faUndo
} from '@fortawesome/free-solid-svg-icons';

// المكونات المحسنة
import {useTheme} from '../components/ThemeProvider';
import {ProductStoreImproved} from '../store/productImproved';
import {toastManager} from '../components/ToastManager';
import {
    LoadingSpinner,
    SkeletonProductDetails,
    LoadingOverlay
} from '../components/LoadingStates';
import ButtonImproved from '../components/ui/ButtonImproved';

import {observer} from 'mobx-react';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

const ProductImproved = observer(({navigation, route}) => {
    // Theme
    const {colors, typography, spacing, shadows} = useTheme();
    
    // Store
    const {
        state: {product, wishlist, cart},
        addToCart,
        addToWishlist,
        removeFromWishlist,
        loadingManager,
        getProductDetails
    } = ProductStoreImproved;

    // حالات المكون
    const [activeSlide, setActiveSlide] = useState(0);
    const [liked, setLiked] = useState(false);
    const [quantity, setQuantity] = useState(1);
    const [selectedVariant, setSelectedVariant] = useState(null);
    const [loading, setLoading] = useState(false);
    const [addingToCart, setAddingToCart] = useState(false);

    // Animations
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const slideAnim = useRef(new Animated.Value(50)).current;
    const heartAnim = useRef(new Animated.Value(1)).current;

    // تحميل تفاصيل المنتج
    useEffect(() => {
        if (route?.params?.productSlug) {
            loadProductDetails(route.params.productSlug);
        }
        
        // Animation للظهور
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 500,
                useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 500,
                useNativeDriver: true,
            })
        ]).start();
    }, [route?.params?.productSlug]);

    // تحديث حالة المفضلة
    useEffect(() => {
        const isLiked = wishlist.find(x => x.id === product.id);
        setLiked(!!isLiked);
    }, [wishlist, product.id]);

    // تحميل تفاصيل المنتج
    const loadProductDetails = async (slug) => {
        setLoading(true);
        try {
            const result = await getProductDetails(slug);
            if (!result.success) {
                toastManager.error('فشل في تحميل تفاصيل المنتج');
                navigation.goBack();
            }
        } catch (error) {
            toastManager.error('حدث خطأ في تحميل المنتج');
            navigation.goBack();
        } finally {
            setLoading(false);
        }
    };

    // إضافة للسلة
    const handleAddToCart = async () => {
        if (!product.inStock) {
            toastManager.warning('المنتج غير متوفر حالياً');
            return;
        }

        setAddingToCart(true);
        try {
            await addToCart(product, quantity);
            
            // Animation للنجاح
            Animated.sequence([
                Animated.timing(slideAnim, {
                    toValue: -10,
                    duration: 100,
                    useNativeDriver: true,
                }),
                Animated.timing(slideAnim, {
                    toValue: 0,
                    duration: 100,
                    useNativeDriver: true,
                })
            ]).start();
            
            toastManager.success(`تم إضافة ${quantity} من ${product.name} للسلة`);
        } catch (error) {
            toastManager.error('فشل في إضافة المنتج للسلة');
        } finally {
            setAddingToCart(false);
        }
    };

    // تبديل المفضلة
    const handleToggleWishlist = async () => {
        // Animation للقلب
        Animated.sequence([
            Animated.timing(heartAnim, {
                toValue: 1.3,
                duration: 150,
                useNativeDriver: true,
            }),
            Animated.timing(heartAnim, {
                toValue: 1,
                duration: 150,
                useNativeDriver: true,
            })
        ]).start();

        try {
            if (liked) {
                await removeFromWishlist(product.id);
                toastManager.info('تم حذف المنتج من المفضلة');
            } else {
                await addToWishlist(product);
                toastManager.success('تم إضافة المنتج للمفضلة');
            }
        } catch (error) {
            toastManager.error('فشل في تحديث المفضلة');
        }
    };

    // مشاركة المنتج
    const handleShare = async () => {
        try {
            await Share.share({
                message: `تحقق من هذا المنتج الرائع: ${product.name}\nالسعر: $${product.price}`,
                title: product.name,
            });
        } catch (error) {
            toastManager.error('فشل في مشاركة المنتج');
        }
    };

    // تغيير الكمية
    const updateQuantity = (change) => {
        const newQuantity = quantity + change;
        if (newQuantity >= 1 && newQuantity <= (product.stock || 10)) {
            setQuantity(newQuantity);
        }
    };

    // عرض النجوم
    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;

        for (let i = 0; i < fullStars; i++) {
            stars.push(
                <FontAwesomeIcon
                    key={i}
                    icon={faStar}
                    size={16}
                    color={colors.yellow[400]}
                />
            );
        }

        if (hasHalfStar) {
            stars.push(
                <FontAwesomeIcon
                    key="half"
                    icon={faStarHalfAlt}
                    size={16}
                    color={colors.yellow[400]}
                />
            );
        }

        return stars;
    };

    // عرض صور المنتج
    const renderImageCarousel = () => {
        if (!product.imgs || product.imgs.length === 0) {
            return (
                <View style={[styles.imagePlaceholder, {backgroundColor: colors.gray[100]}]}>
                    <Text style={[styles.imagePlaceholderText, {color: colors.gray[400]}]}>
                        لا توجد صورة
                    </Text>
                </View>
            );
        }

        return (
            <View style={styles.carouselContainer}>
                <Carousel
                    data={product.imgs}
                    renderItem={({item}) => (
                        <Image
                            source={{uri: item}}
                            style={styles.productImage}
                            resizeMode="cover"
                        />
                    )}
                    sliderWidth={screenWidth}
                    itemWidth={screenWidth}
                    onSnapToItem={setActiveSlide}
                    autoplay={true}
                    autoplayDelay={3000}
                    loop={true}
                />
                
                <Pagination
                    dotsLength={product.imgs.length}
                    activeDotIndex={activeSlide}
                    containerStyle={styles.paginationContainer}
                    dotStyle={[styles.paginationDot, {backgroundColor: colors.primary[600]}]}
                    inactiveDotStyle={[styles.paginationInactiveDot, {backgroundColor: colors.gray[300]}]}
                    inactiveDotOpacity={0.6}
                    inactiveDotScale={0.8}
                />
            </View>
        );
    };

    // عرض معلومات المنتج
    const renderProductInfo = () => (
        <Animated.View
            style={[
                styles.productInfo,
                {
                    backgroundColor: colors.surface,
                    opacity: fadeAnim,
                    transform: [{translateY: slideAnim}]
                }
            ]}
        >
            {/* اسم المنتج والسعر */}
            <View style={styles.productHeader}>
                <Text style={[styles.productName, {color: colors.text}]}>
                    {product.name}
                </Text>
                
                <View style={styles.priceContainer}>
                    <Text style={[styles.currentPrice, {color: colors.primary[600]}]}>
                        ${product.price}
                    </Text>
                    {product.originalPrice && (
                        <Text style={[styles.originalPrice, {color: colors.gray[500]}]}>
                            ${product.originalPrice}
                        </Text>
                    )}
                    {product.discount && (
                        <View style={[styles.discountBadge, {backgroundColor: colors.error[500]}]}>
                            <Text style={styles.discountText}>
                                -{product.discount}%
                            </Text>
                        </View>
                    )}
                </View>
            </View>

            {/* التقييم */}
            {product.rating && (
                <View style={styles.ratingContainer}>
                    <View style={styles.starsContainer}>
                        {renderStars(product.rating)}
                    </View>
                    <Text style={[styles.ratingText, {color: colors.gray[600]}]}>
                        ({product.reviewCount || 0} تقييم)
                    </Text>
                </View>
            )}

            {/* حالة التوفر */}
            <View style={styles.stockContainer}>
                <FontAwesomeIcon
                    icon={product.inStock ? faCheck : faExclamationTriangle}
                    size={16}
                    color={product.inStock ? colors.green[500] : colors.red[500]}
                />
                <Text style={[
                    styles.stockText,
                    {color: product.inStock ? colors.green[600] : colors.red[600]}
                ]}>
                    {product.inStock ? 'متوفر' : 'غير متوفر'}
                </Text>
                {product.stock && (
                    <Text style={[styles.stockCount, {color: colors.gray[500]}]}>
                        (متبقي {product.stock})
                    </Text>
                )}
            </View>

            {/* الوصف */}
            {product.description && (
                <View style={styles.descriptionContainer}>
                    <Text style={[styles.descriptionTitle, {color: colors.text}]}>
                        الوصف
                    </Text>
                    <Text style={[styles.descriptionText, {color: colors.gray[700]}]}>
                        {product.description}
                    </Text>
                </View>
            )}

            {/* الميزات */}
            <View style={styles.featuresContainer}>
                <View style={styles.featureItem}>
                    <FontAwesomeIcon icon={faShieldAlt} size={16} color={colors.green[500]} />
                    <Text style={[styles.featureText, {color: colors.gray[700]}]}>
                        ضمان لمدة سنة
                    </Text>
                </View>
                <View style={styles.featureItem}>
                    <FontAwesomeIcon icon={faTruck} size={16} color={colors.blue[500]} />
                    <Text style={[styles.featureText, {color: colors.gray[700]}]}>
                        شحن مجاني
                    </Text>
                </View>
                <View style={styles.featureItem}>
                    <FontAwesomeIcon icon={faUndo} size={16} color={colors.orange[500]} />
                    <Text style={[styles.featureText, {color: colors.gray[700]}]}>
                        إرجاع خلال 30 يوم
                    </Text>
                </View>
            </View>
        </Animated.View>
    );

    // عرض أزرار الكمية والإجراءات
    const renderActions = () => (
        <Animated.View
            style={[
                styles.actionsContainer,
                {
                    backgroundColor: colors.surface,
                    borderTopColor: colors.border,
                    opacity: fadeAnim,
                    transform: [{translateY: slideAnim}]
                }
            ]}
        >
            {/* تحديد الكمية */}
            <View style={styles.quantityContainer}>
                <Text style={[styles.quantityLabel, {color: colors.text}]}>
                    الكمية:
                </Text>
                <View style={[styles.quantityControls, {borderColor: colors.border}]}>
                    <TouchableOpacity
                        onPress={() => updateQuantity(-1)}
                        style={[styles.quantityButton, {backgroundColor: colors.gray[100]}]}
                        disabled={quantity <= 1}
                    >
                        <FontAwesomeIcon
                            icon={faMinus}
                            size={14}
                            color={quantity <= 1 ? colors.gray[400] : colors.gray[700]}
                        />
                    </TouchableOpacity>
                    
                    <Text style={[styles.quantityText, {color: colors.text}]}>
                        {quantity}
                    </Text>
                    
                    <TouchableOpacity
                        onPress={() => updateQuantity(1)}
                        style={[styles.quantityButton, {backgroundColor: colors.gray[100]}]}
                        disabled={quantity >= (product.stock || 10)}
                    >
                        <FontAwesomeIcon
                            icon={faPlus}
                            size={14}
                            color={quantity >= (product.stock || 10) ? colors.gray[400] : colors.gray[700]}
                        />
                    </TouchableOpacity>
                </View>
            </View>

            {/* أزرار الإجراءات */}
            <View style={styles.actionButtons}>
                <TouchableOpacity
                    onPress={handleToggleWishlist}
                    style={[styles.wishlistButton, {backgroundColor: colors.gray[100]}]}
                >
                    <Animated.View style={{transform: [{scale: heartAnim}]}}>
                        <FontAwesomeIcon
                            icon={liked ? faHeartFilled : faHeart}
                            size={20}
                            color={liked ? colors.red[500] : colors.gray[500]}
                        />
                    </Animated.View>
                </TouchableOpacity>

                <TouchableOpacity
                    onPress={handleShare}
                    style={[styles.shareButton, {backgroundColor: colors.gray[100]}]}
                >
                    <FontAwesomeIcon
                        icon={faShare}
                        size={18}
                        color={colors.gray[600]}
                    />
                </TouchableOpacity>

                <ButtonImproved
                    variant="primary"
                    size="large"
                    onPress={handleAddToCart}
                    loading={addingToCart}
                    disabled={!product.inStock}
                    style={styles.addToCartButton}
                    icon={faCartShopping}
                >
                    إضافة للسلة
                </ButtonImproved>
            </View>
        </Animated.View>
    );

    if (loading) {
        return (
            <SafeAreaView style={[styles.container, {backgroundColor: colors.background}]}>
                <SkeletonProductDetails />
            </SafeAreaView>
        );
    }

    if (!product || !product.id) {
        return (
            <SafeAreaView style={[styles.container, {backgroundColor: colors.background}]}>
                <View style={styles.errorContainer}>
                    <FontAwesomeIcon
                        icon={faExclamationTriangle}
                        size={48}
                        color={colors.gray[400]}
                    />
                    <Text style={[styles.errorText, {color: colors.gray[600]}]}>
                        المنتج غير موجود
                    </Text>
                    <ButtonImproved
                        variant="outline"
                        onPress={() => navigation.goBack()}
                        style={styles.backButton}
                    >
                        العودة
                    </ButtonImproved>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView style={[styles.container, {backgroundColor: colors.background}]}>
            <StatusBar barStyle="dark-content" backgroundColor={colors.surface} />
            
            {/* Header */}
            <View style={[styles.header, {backgroundColor: colors.surface, borderBottomColor: colors.border}]}>
                <TouchableOpacity
                    onPress={() => navigation.goBack()}
                    style={styles.backButton}
                >
                    <FontAwesomeIcon
                        icon={faArrowLeft}
                        size={20}
                        color={colors.text}
                    />
                </TouchableOpacity>
                
                <Text style={[styles.headerTitle, {color: colors.text}]}>
                    تفاصيل المنتج
                </Text>
                
                <View style={styles.headerSpacer} />
            </View>

            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                {/* صور المنتج */}
                {renderImageCarousel()}
                
                {/* معلومات المنتج */}
                {renderProductInfo()}
            </ScrollView>

            {/* أزرار الإجراءات */}
            {renderActions()}

            {/* Loading Overlay */}
            <LoadingOverlay
                visible={loadingManager.getLoadingState('getProductDetails')}
                text="جاري تحميل تفاصيل المنتج..."
            />
        </SafeAreaView>
    );
});

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },

    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
    },

    backButton: {
        padding: 8,
    },

    headerTitle: {
        flex: 1,
        fontSize: 18,
        fontFamily: 'Cairo-SemiBold',
        textAlign: 'center',
    },

    headerSpacer: {
        width: 36,
    },

    scrollView: {
        flex: 1,
    },

    carouselContainer: {
        position: 'relative',
    },

    productImage: {
        width: screenWidth,
        height: 300,
    },

    imagePlaceholder: {
        width: screenWidth,
        height: 300,
        justifyContent: 'center',
        alignItems: 'center',
    },

    imagePlaceholderText: {
        fontSize: 16,
        fontFamily: 'Cairo-Regular',
    },

    paginationContainer: {
        position: 'absolute',
        bottom: 10,
        alignSelf: 'center',
    },

    paginationDot: {
        width: 8,
        height: 8,
        borderRadius: 4,
    },

    paginationInactiveDot: {
        width: 6,
        height: 6,
        borderRadius: 3,
    },

    productInfo: {
        padding: 20,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        marginTop: -20,
    },

    productHeader: {
        marginBottom: 16,
    },

    productName: {
        fontSize: 24,
        fontFamily: 'Cairo-Bold',
        textAlign: 'right',
        marginBottom: 8,
    },

    priceContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        gap: 8,
    },

    currentPrice: {
        fontSize: 20,
        fontFamily: 'Cairo-Bold',
    },

    originalPrice: {
        fontSize: 16,
        textDecorationLine: 'line-through',
    },

    discountBadge: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 4,
    },

    discountText: {
        color: '#fff',
        fontSize: 12,
        fontFamily: 'Cairo-SemiBold',
    },

    ratingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        marginBottom: 16,
        gap: 8,
    },

    starsContainer: {
        flexDirection: 'row',
        gap: 2,
    },

    ratingText: {
        fontSize: 14,
        fontFamily: 'Cairo-Regular',
    },

    stockContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        marginBottom: 20,
        gap: 8,
    },

    stockText: {
        fontSize: 16,
        fontFamily: 'Cairo-SemiBold',
    },

    stockCount: {
        fontSize: 14,
        fontFamily: 'Cairo-Regular',
    },

    descriptionContainer: {
        marginBottom: 20,
    },

    descriptionTitle: {
        fontSize: 18,
        fontFamily: 'Cairo-SemiBold',
        textAlign: 'right',
        marginBottom: 8,
    },

    descriptionText: {
        fontSize: 16,
        fontFamily: 'Cairo-Regular',
        lineHeight: 24,
        textAlign: 'right',
    },

    featuresContainer: {
        gap: 12,
    },

    featureItem: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        gap: 8,
    },

    featureText: {
        fontSize: 14,
        fontFamily: 'Cairo-Regular',
    },

    actionsContainer: {
        padding: 20,
        borderTopWidth: 1,
        gap: 16,
    },

    quantityContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },

    quantityLabel: {
        fontSize: 16,
        fontFamily: 'Cairo-SemiBold',
    },

    quantityControls: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 8,
        overflow: 'hidden',
    },

    quantityButton: {
        padding: 12,
        minWidth: 44,
        alignItems: 'center',
        justifyContent: 'center',
    },

    quantityText: {
        paddingHorizontal: 20,
        fontSize: 16,
        fontFamily: 'Cairo-SemiBold',
        minWidth: 60,
        textAlign: 'center',
    },

    actionButtons: {
        flexDirection: 'row',
        gap: 12,
    },

    wishlistButton: {
        width: 48,
        height: 48,
        borderRadius: 24,
        alignItems: 'center',
        justifyContent: 'center',
    },

    shareButton: {
        width: 48,
        height: 48,
        borderRadius: 24,
        alignItems: 'center',
        justifyContent: 'center',
    },

    addToCartButton: {
        flex: 1,
    },

    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        gap: 16,
    },

    errorText: {
        fontSize: 18,
        fontFamily: 'Cairo-Regular',
        textAlign: 'center',
    },
});

export default ProductImproved;
