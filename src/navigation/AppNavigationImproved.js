import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>ontainer, DefaultTheme, DarkTheme} from '@react-navigation/native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {createStackNavigator} from '@react-navigation/stack';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Animated,
    Platform,
    StatusBar
} from 'react-native';

import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
    faHome,
    faSearch,
    faShoppingCart,
    faUser,
    faHeart,
    faArrowLeft,
    faBars,
    faShoppingBag
} from '@fortawesome/free-solid-svg-icons';

// المكونات والشاشات المحسنة
import {useTheme} from '../components/ThemeProvider';
import {ProductStoreImproved} from '../store/productImproved';
import {toastManager} from '../components/ToastManager';

// الشاشات المحسنة
import DashboardImproved from '../screens/DashboardImproved';
import SearchImproved from '../screens/SearchImproved';
import ProductImproved from '../screens/ProductImproved';
import CartImproved from '../screens/CartImproved';
import ProfileImproved from '../screens/ProfileImproved';

// الشاشات الأصلية (للتوافق)
import Category from '../screens/Category';
import Wishlist from '../screens/Wishlist';

import {observer} from 'mobx-react';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// مكون Tab Bar مخصص ومحسن
const CustomTabBar = observer(({state, descriptors, navigation}) => {
    const {colors, typography, spacing} = useTheme();
    const cartItemsCount = ProductStoreImproved.cartItemsCount;

    return (
        <View style={[styles.tabBar, {backgroundColor: colors.surface, borderTopColor: colors.border}]}>
            {state.routes.map((route, index) => {
                const {options} = descriptors[route.key];
                const label = options.tabBarLabel || options.title || route.name;
                const isFocused = state.index === index;

                // أيقونات التبويبات
                const getTabIcon = () => {
                    switch (route.name) {
                        case 'Dashboard':
                            return faHome;
                        case 'Search':
                            return faSearch;
                        case 'Cart':
                            return faShoppingCart;
                        case 'Wishlist':
                            return faHeart;
                        case 'Profile':
                            return faUser;
                        default:
                            return faHome;
                    }
                };

                const onPress = () => {
                    const event = navigation.emit({
                        type: 'tabPress',
                        target: route.key,
                        canPreventDefault: true,
                    });

                    if (!isFocused && !event.defaultPrevented) {
                        navigation.navigate(route.name);
                    }
                };

                const onLongPress = () => {
                    navigation.emit({
                        type: 'tabLongPress',
                        target: route.key,
                    });
                };

                return (
                    <TouchableOpacity
                        key={route.key}
                        accessibilityRole="button"
                        accessibilityState={isFocused ? {selected: true} : {}}
                        accessibilityLabel={options.tabBarAccessibilityLabel}
                        testID={options.tabBarTestID}
                        onPress={onPress}
                        onLongPress={onLongPress}
                        style={[
                            styles.tabItem,
                            isFocused && [styles.tabItemActive, {backgroundColor: colors.primary[50]}]
                        ]}
                    >
                        <View style={styles.tabIconContainer}>
                            <FontAwesomeIcon
                                icon={getTabIcon()}
                                size={20}
                                color={isFocused ? colors.primary[600] : colors.gray[400]}
                            />
                            
                            {/* Badge للسلة */}
                            {route.name === 'Cart' && cartItemsCount > 0 && (
                                <View style={[styles.badge, {backgroundColor: colors.error[500]}]}>
                                    <Text style={[styles.badgeText, {color: colors.white}]}>
                                        {cartItemsCount > 99 ? '99+' : cartItemsCount}
                                    </Text>
                                </View>
                            )}
                        </View>
                        
                        <Text style={[
                            styles.tabLabel,
                            {
                                color: isFocused ? colors.primary[600] : colors.gray[500],
                                fontFamily: isFocused ? typography.fontFamily.primarySemiBold : typography.fontFamily.primary
                            }
                        ]}>
                            {label}
                        </Text>
                    </TouchableOpacity>
                );
            })}
        </View>
    );
});

// مكون Header مخصص
const CustomHeader = ({title, navigation, showBack = false, rightComponent = null}) => {
    const {colors, typography} = useTheme();

    return (
        <View style={[styles.header, {backgroundColor: colors.surface, borderBottomColor: colors.border}]}>
            <View style={styles.headerLeft}>
                {showBack ? (
                    <TouchableOpacity
                        onPress={() => navigation.goBack()}
                        style={styles.headerButton}
                    >
                        <FontAwesomeIcon
                            icon={faArrowLeft}
                            size={20}
                            color={colors.text}
                        />
                    </TouchableOpacity>
                ) : (
                    <TouchableOpacity
                        onPress={() => navigation.openDrawer?.()}
                        style={styles.headerButton}
                    >
                        <FontAwesomeIcon
                            icon={faBars}
                            size={20}
                            color={colors.text}
                        />
                    </TouchableOpacity>
                )}
            </View>

            <Text style={[styles.headerTitle, {color: colors.text, fontFamily: typography.fontFamily.primaryBold}]}>
                {title}
            </Text>

            <View style={styles.headerRight}>
                {rightComponent}
            </View>
        </View>
    );
};

// التنقل السفلي المحسن
const TabNavigatorImproved = observer(() => {
    const {colors, isDarkTheme} = useTheme();

    return (
        <Tab.Navigator
            tabBar={(props) => <CustomTabBar {...props} />}
            screenOptions={{
                headerShown: false,
                tabBarHideOnKeyboard: Platform.OS === 'android',
            }}
            initialRouteName="Dashboard"
        >
            <Tab.Screen
                name="Dashboard"
                component={DashboardImproved}
                options={{
                    tabBarLabel: 'الرئيسية',
                }}
            />
            <Tab.Screen
                name="Search"
                component={SearchImproved}
                options={{
                    tabBarLabel: 'البحث',
                }}
            />
            <Tab.Screen
                name="Cart"
                component={CartImproved}
                options={{
                    tabBarLabel: 'السلة',
                }}
            />
            <Tab.Screen
                name="Wishlist"
                component={Wishlist}
                options={{
                    tabBarLabel: 'المفضلة',
                }}
            />
            <Tab.Screen
                name="Profile"
                component={ProfileImproved}
                options={{
                    tabBarLabel: 'الحساب',
                }}
            />
        </Tab.Navigator>
    );
});

// التنقل الرئيسي المحسن
const AppNavigationImproved = observer(() => {
    const {theme, colors, isDarkTheme} = useTheme();
    const [isReady, setIsReady] = useState(false);

    // إعداد الثيم للـ Navigation
    const navigationTheme = {
        ...isDarkTheme() ? DarkTheme : DefaultTheme,
        colors: {
            ...isDarkTheme() ? DarkTheme.colors : DefaultTheme.colors,
            primary: colors.primary[600],
            background: colors.background,
            card: colors.surface,
            text: colors.text,
            border: colors.border,
            notification: colors.error[500],
        },
    };

    // تحديث شريط الحالة
    useEffect(() => {
        StatusBar.setBarStyle(isDarkTheme() ? 'light-content' : 'dark-content', true);
        
        if (Platform.OS === 'android') {
            StatusBar.setBackgroundColor(colors.surface, true);
        }
    }, [isDarkTheme, colors.surface]);

    // معالج حالة التنقل
    const onStateChange = (state) => {
        // يمكن إضافة analytics هنا
        console.log('Navigation state changed:', state);
    };

    // معالج الاستعداد
    const onReady = () => {
        setIsReady(true);
        toastManager.success('تم تحميل التطبيق بنجاح');
    };

    return (
        <NavigationContainer
            theme={navigationTheme}
            onStateChange={onStateChange}
            onReady={onReady}
        >
            <Stack.Navigator
                screenOptions={{
                    headerShown: false,
                    cardStyleInterpolator: ({current, layouts}) => {
                        return {
                            cardStyle: {
                                transform: [
                                    {
                                        translateX: current.progress.interpolate({
                                            inputRange: [0, 1],
                                            outputRange: [layouts.screen.width, 0],
                                        }),
                                    },
                                ],
                            },
                        };
                    },
                    transitionSpec: {
                        open: {
                            animation: 'timing',
                            config: {
                                duration: 300,
                            },
                        },
                        close: {
                            animation: 'timing',
                            config: {
                                duration: 300,
                            },
                        },
                    },
                }}
            >
                {/* الشاشة الرئيسية */}
                <Stack.Screen
                    name="Main"
                    component={TabNavigatorImproved}
                />

                {/* شاشة تفاصيل المنتج */}
                <Stack.Screen
                    name="Product"
                    component={ProductImproved}
                    options={{
                        headerShown: true,
                        header: ({navigation}) => (
                            <CustomHeader
                                title="تفاصيل المنتج"
                                navigation={navigation}
                                showBack={true}
                                rightComponent={
                                    <TouchableOpacity
                                        onPress={() => navigation.navigate('Cart')}
                                        style={styles.headerButton}
                                    >
                                        <FontAwesomeIcon
                                            icon={faShoppingBag}
                                            size={20}
                                            color={colors.text}
                                        />
                                    </TouchableOpacity>
                                }
                            />
                        ),
                    }}
                />

                {/* شاشة الفئة */}
                <Stack.Screen
                    name="Category"
                    component={Category}
                    options={{
                        headerShown: true,
                        header: ({navigation, route}) => (
                            <CustomHeader
                                title={route.params?.categoryName || 'الفئة'}
                                navigation={navigation}
                                showBack={true}
                            />
                        ),
                    }}
                />
            </Stack.Navigator>
        </NavigationContainer>
    );
});

const styles = StyleSheet.create({
    // Tab Bar Styles
    tabBar: {
        flexDirection: 'row',
        borderTopWidth: 1,
        paddingBottom: Platform.OS === 'ios' ? 20 : 10,
        paddingTop: 10,
        paddingHorizontal: 5,
        elevation: 8,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: -2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
    },

    tabItem: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 8,
        paddingHorizontal: 4,
        borderRadius: 12,
        marginHorizontal: 2,
    },

    tabItemActive: {
        borderRadius: 12,
    },

    tabIconContainer: {
        position: 'relative',
        marginBottom: 4,
    },

    badge: {
        position: 'absolute',
        top: -8,
        right: -8,
        minWidth: 18,
        height: 18,
        borderRadius: 9,
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 4,
    },

    badgeText: {
        fontSize: 10,
        fontFamily: 'Cairo-Bold',
        lineHeight: 12,
    },

    tabLabel: {
        fontSize: 11,
        textAlign: 'center',
        marginTop: 2,
    },

    // Header Styles
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
        paddingTop: Platform.OS === 'ios' ? 50 : 12,
    },

    headerLeft: {
        width: 40,
        alignItems: 'flex-start',
    },

    headerTitle: {
        flex: 1,
        fontSize: 18,
        textAlign: 'center',
    },

    headerRight: {
        width: 40,
        alignItems: 'flex-end',
    },

    headerButton: {
        padding: 8,
        borderRadius: 8,
    },
});

export default AppNavigationImproved;
