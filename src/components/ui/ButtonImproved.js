import React from 'react';
import {
    TouchableOpacity,
    Text,
    View,
    StyleSheet,
    ActivityIndicator
} from 'react-native';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';

import {theme} from '../../styles/theme';

const ButtonImproved = ({
    children,
    variant = 'primary',
    size = 'medium',
    disabled = false,
    loading = false,
    onPress,
    style = {},
    textStyle = {},
    icon = null,
    iconPosition = 'left',
    fullWidth = false,
    ...props
}) => {
    const getButtonStyles = () => {
        const baseStyles = [styles.button];
        
        // Variant styles
        switch (variant) {
            case 'primary':
                baseStyles.push(styles.buttonPrimary);
                break;
            case 'secondary':
                baseStyles.push(styles.buttonSecondary);
                break;
            case 'outline':
                baseStyles.push(styles.buttonOutline);
                break;
            case 'danger':
                baseStyles.push(styles.buttonDanger);
                break;
            case 'success':
                baseStyles.push(styles.buttonSuccess);
                break;
            case 'warning':
                baseStyles.push(styles.buttonWarning);
                break;
            case 'ghost':
                baseStyles.push(styles.buttonGhost);
                break;
            default:
                baseStyles.push(styles.buttonPrimary);
        }
        
        // Size styles
        switch (size) {
            case 'small':
                baseStyles.push(styles.buttonSmall);
                break;
            case 'medium':
                baseStyles.push(styles.buttonMedium);
                break;
            case 'large':
                baseStyles.push(styles.buttonLarge);
                break;
        }
        
        // State styles
        if (disabled) {
            baseStyles.push(styles.buttonDisabled);
        }
        
        if (fullWidth) {
            baseStyles.push(styles.buttonFullWidth);
        }
        
        // Custom styles
        baseStyles.push(style);
        
        return baseStyles;
    };

    const getTextStyles = () => {
        const baseStyles = [styles.buttonText];
        
        // Variant text styles
        switch (variant) {
            case 'primary':
                baseStyles.push(styles.buttonTextPrimary);
                break;
            case 'secondary':
                baseStyles.push(styles.buttonTextSecondary);
                break;
            case 'outline':
                baseStyles.push(styles.buttonTextOutline);
                break;
            case 'danger':
                baseStyles.push(styles.buttonTextDanger);
                break;
            case 'success':
                baseStyles.push(styles.buttonTextSuccess);
                break;
            case 'warning':
                baseStyles.push(styles.buttonTextWarning);
                break;
            case 'ghost':
                baseStyles.push(styles.buttonTextGhost);
                break;
        }
        
        // Size text styles
        switch (size) {
            case 'small':
                baseStyles.push(styles.buttonTextSmall);
                break;
            case 'medium':
                baseStyles.push(styles.buttonTextMedium);
                break;
            case 'large':
                baseStyles.push(styles.buttonTextLarge);
                break;
        }
        
        if (disabled) {
            baseStyles.push(styles.buttonTextDisabled);
        }
        
        baseStyles.push(textStyle);
        
        return baseStyles;
    };

    const getIconSize = () => {
        switch (size) {
            case 'small':
                return 14;
            case 'medium':
                return 16;
            case 'large':
                return 18;
            default:
                return 16;
        }
    };

    const getIconColor = () => {
        if (disabled) return theme.colors.gray[400];
        
        switch (variant) {
            case 'primary':
            case 'danger':
            case 'success':
            case 'warning':
                return '#fff';
            case 'secondary':
                return theme.colors.gray[700];
            case 'outline':
                return theme.colors.primary[600];
            case 'ghost':
                return theme.colors.primary[600];
            default:
                return '#fff';
        }
    };

    const renderContent = () => {
        if (loading) {
            return (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator
                        size="small"
                        color={getIconColor()}
                        style={styles.loadingSpinner}
                    />
                    <Text style={getTextStyles()}>
                        جاري التحميل...
                    </Text>
                </View>
            );
        }

        const content = [];
        
        // Left icon
        if (icon && iconPosition === 'left') {
            content.push(
                <FontAwesomeIcon
                    key="icon-left"
                    icon={icon}
                    size={getIconSize()}
                    color={getIconColor()}
                    style={styles.iconLeft}
                />
            );
        }

        // Text content
        if (typeof children === 'string') {
            content.push(
                <Text key="text" style={getTextStyles()}>
                    {children}
                </Text>
            );
        } else {
            content.push(React.cloneElement(children, { key: 'content' }));
        }

        // Right icon
        if (icon && iconPosition === 'right') {
            content.push(
                <FontAwesomeIcon
                    key="icon-right"
                    icon={icon}
                    size={getIconSize()}
                    color={getIconColor()}
                    style={styles.iconRight}
                />
            );
        }

        return content;
    };

    return (
        <TouchableOpacity
            style={getButtonStyles()}
            onPress={onPress}
            disabled={disabled || loading}
            activeOpacity={disabled ? 1 : 0.8}
            {...props}
        >
            <View style={styles.buttonContent}>
                {renderContent()}
            </View>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    button: {
        borderRadius: theme.borderRadius.md,
        borderWidth: 1,
        borderColor: 'transparent',
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden',
    },

    buttonContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },

    // Variant Styles
    buttonPrimary: {
        backgroundColor: theme.colors.primary[600],
        borderColor: theme.colors.primary[600],
    },

    buttonSecondary: {
        backgroundColor: theme.colors.gray[100],
        borderColor: theme.colors.gray[200],
    },

    buttonOutline: {
        backgroundColor: 'transparent',
        borderColor: theme.colors.primary[600],
    },

    buttonDanger: {
        backgroundColor: theme.colors.red[600],
        borderColor: theme.colors.red[600],
    },

    buttonSuccess: {
        backgroundColor: theme.colors.green[600],
        borderColor: theme.colors.green[600],
    },

    buttonWarning: {
        backgroundColor: theme.colors.yellow[500],
        borderColor: theme.colors.yellow[500],
    },

    buttonGhost: {
        backgroundColor: 'transparent',
        borderColor: 'transparent',
    },

    // Size Styles
    buttonSmall: {
        paddingHorizontal: theme.spacing.sm,
        paddingVertical: theme.spacing.xs,
        minHeight: 32,
    },

    buttonMedium: {
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        minHeight: 44,
    },

    buttonLarge: {
        paddingHorizontal: theme.spacing.lg,
        paddingVertical: theme.spacing.md,
        minHeight: 52,
    },

    // State Styles
    buttonDisabled: {
        opacity: 0.6,
    },

    buttonFullWidth: {
        width: '100%',
    },

    // Text Styles
    buttonText: {
        fontFamily: theme.typography.fontFamily.primarySemiBold,
        textAlign: 'center',
    },

    buttonTextPrimary: {
        color: '#fff',
    },

    buttonTextSecondary: {
        color: theme.colors.gray[700],
    },

    buttonTextOutline: {
        color: theme.colors.primary[600],
    },

    buttonTextDanger: {
        color: '#fff',
    },

    buttonTextSuccess: {
        color: '#fff',
    },

    buttonTextWarning: {
        color: '#fff',
    },

    buttonTextGhost: {
        color: theme.colors.primary[600],
    },

    buttonTextSmall: {
        fontSize: theme.typography.fontSize.sm,
    },

    buttonTextMedium: {
        fontSize: theme.typography.fontSize.md,
    },

    buttonTextLarge: {
        fontSize: theme.typography.fontSize.lg,
    },

    buttonTextDisabled: {
        opacity: 0.6,
    },

    // Icon Styles
    iconLeft: {
        marginRight: theme.spacing.xs,
    },

    iconRight: {
        marginLeft: theme.spacing.xs,
    },

    // Loading Styles
    loadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },

    loadingSpinner: {
        marginRight: theme.spacing.xs,
    },
});

export default ButtonImproved;
