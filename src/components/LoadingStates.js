import React, {useEffect, useRef} from 'react';
import {
    View,
    Text,
    Animated,
    StyleSheet,
    Dimensions,
    ActivityIndicator
} from 'react-native';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faSpinner} from '@fortawesome/free-solid-svg-icons';

import {theme} from '../styles/theme';

const {width} = Dimensions.get('window');

// =============== Loading Spinner ===============
export const LoadingSpinner = ({
    size = 'medium',
    color = theme.colors.primary[600],
    text = null,
    style = {}
}) => {
    const sizeMap = {
        small: 20,
        medium: 30,
        large: 40
    };

    return (
        <View style={[styles.spinnerContainer, style]}>
            <ActivityIndicator 
                size={sizeMap[size]} 
                color={color}
            />
            {text && (
                <Text style={[styles.spinnerText, {color}]}>
                    {text}
                </Text>
            )}
        </View>
    );
};

// =============== Skeleton Components ===============

// Skeleton أساسي
const SkeletonBase = ({width, height, style = {}}) => {
    const opacity = useRef(new Animated.Value(0.3)).current;

    useEffect(() => {
        const animation = Animated.loop(
            Animated.sequence([
                Animated.timing(opacity, {
                    toValue: 1,
                    duration: 800,
                    useNativeDriver: true,
                }),
                Animated.timing(opacity, {
                    toValue: 0.3,
                    duration: 800,
                    useNativeDriver: true,
                })
            ])
        );
        animation.start();

        return () => animation.stop();
    }, []);

    return (
        <Animated.View
            style={[
                styles.skeleton,
                {
                    width,
                    height,
                    opacity
                },
                style
            ]}
        />
    );
};

// Skeleton للنص
export const SkeletonText = ({
    lines = 1,
    width = '100%',
    height = 16,
    spacing = 8,
    style = {}
}) => {
    return (
        <View style={[styles.skeletonTextContainer, style]}>
            {Array.from({length: lines}).map((_, index) => (
                <SkeletonBase
                    key={index}
                    width={index === lines - 1 ? '70%' : width}
                    height={height}
                    style={{marginBottom: index < lines - 1 ? spacing : 0}}
                />
            ))}
        </View>
    );
};

// Skeleton للصورة
export const SkeletonImage = ({
    width = 100,
    height = 100,
    borderRadius = theme.borderRadius.md,
    style = {}
}) => {
    return (
        <SkeletonBase
            width={width}
            height={height}
            style={[{borderRadius}, style]}
        />
    );
};

// Skeleton لبطاقة المنتج
export const SkeletonProductCard = ({style = {}}) => {
    return (
        <View style={[styles.productCardSkeleton, style]}>
            {/* صورة المنتج */}
            <SkeletonImage
                width="100%"
                height={150}
                borderRadius={theme.borderRadius.md}
                style={styles.productImageSkeleton}
            />
            
            {/* اسم المنتج */}
            <SkeletonText
                lines={2}
                height={14}
                spacing={6}
                style={styles.productNameSkeleton}
            />
            
            {/* السعر */}
            <SkeletonText
                lines={1}
                width="40%"
                height={16}
                style={styles.productPriceSkeleton}
            />
            
            {/* زر الإضافة */}
            <SkeletonBase
                width="100%"
                height={36}
                style={styles.productButtonSkeleton}
            />
        </View>
    );
};

// Skeleton لقائمة المنتجات
export const SkeletonProductGrid = ({
    itemsPerRow = 2,
    rows = 3,
    style = {}
}) => {
    const totalItems = itemsPerRow * rows;
    
    return (
        <View style={[styles.productGridSkeleton, style]}>
            {Array.from({length: totalItems}).map((_, index) => (
                <SkeletonProductCard
                    key={index}
                    style={[
                        styles.gridItemSkeleton,
                        {width: (width - 45) / itemsPerRow}
                    ]}
                />
            ))}
        </View>
    );
};

// Skeleton للفئات
export const SkeletonCategories = ({count = 5, style = {}}) => {
    return (
        <View style={[styles.categoriesSkeleton, style]}>
            {Array.from({length: count}).map((_, index) => (
                <View key={index} style={styles.categorySkeleton}>
                    <SkeletonImage
                        width={40}
                        height={40}
                        borderRadius={20}
                        style={styles.categoryIconSkeleton}
                    />
                    <SkeletonText
                        lines={1}
                        width={60}
                        height={12}
                        style={styles.categoryTextSkeleton}
                    />
                </View>
            ))}
        </View>
    );
};

// Skeleton لصفحة المنتج
export const SkeletonProductDetails = ({style = {}}) => {
    return (
        <View style={[styles.productDetailsSkeleton, style]}>
            {/* صور المنتج */}
            <SkeletonImage
                width="100%"
                height={300}
                style={styles.productMainImageSkeleton}
            />
            
            {/* معلومات المنتج */}
            <View style={styles.productInfoSkeleton}>
                <SkeletonText
                    lines={2}
                    height={20}
                    spacing={8}
                    style={styles.productTitleSkeleton}
                />
                
                <SkeletonText
                    lines={1}
                    width="30%"
                    height={24}
                    style={styles.productPriceDetailsSkeleton}
                />
                
                <SkeletonText
                    lines={4}
                    height={14}
                    spacing={6}
                    style={styles.productDescriptionSkeleton}
                />
                
                {/* أزرار الإجراءات */}
                <View style={styles.productActionsSkeleton}>
                    <SkeletonBase
                        width="48%"
                        height={44}
                        style={styles.actionButtonSkeleton}
                    />
                    <SkeletonBase
                        width="48%"
                        height={44}
                        style={styles.actionButtonSkeleton}
                    />
                </View>
            </View>
        </View>
    );
};

// =============== Loading Overlay ===============
export const LoadingOverlay = ({
    visible = false,
    text = 'جاري التحميل...',
    transparent = true,
    style = {}
}) => {
    if (!visible) return null;

    return (
        <View style={[
            styles.overlay,
            transparent && styles.transparentOverlay,
            style
        ]}>
            <View style={styles.overlayContent}>
                <LoadingSpinner size="large" />
                <Text style={styles.overlayText}>{text}</Text>
            </View>
        </View>
    );
};

// =============== Pull to Refresh Indicator ===============
export const PullToRefreshIndicator = ({
    refreshing = false,
    onRefresh,
    children,
    style = {}
}) => {
    return (
        <View style={[styles.pullToRefreshContainer, style]}>
            {refreshing && (
                <View style={styles.refreshIndicator}>
                    <LoadingSpinner size="small" />
                    <Text style={styles.refreshText}>جاري التحديث...</Text>
                </View>
            )}
            {children}
        </View>
    );
};

const styles = StyleSheet.create({
    // Spinner Styles
    spinnerContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: theme.spacing.md,
    },

    spinnerText: {
        marginTop: theme.spacing.sm,
        fontSize: theme.typography.fontSize.sm,
        fontFamily: theme.typography.fontFamily.primary,
    },

    // Skeleton Base Styles
    skeleton: {
        backgroundColor: theme.colors.gray[200],
        borderRadius: theme.borderRadius.sm,
    },

    skeletonTextContainer: {
        // Container for text skeletons
    },

    // Product Card Skeleton
    productCardSkeleton: {
        backgroundColor: '#fff',
        borderRadius: theme.borderRadius.lg,
        padding: theme.spacing.md,
        marginBottom: theme.spacing.md,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },

    productImageSkeleton: {
        marginBottom: theme.spacing.md,
    },

    productNameSkeleton: {
        marginBottom: theme.spacing.sm,
    },

    productPriceSkeleton: {
        marginBottom: theme.spacing.md,
    },

    productButtonSkeleton: {
        borderRadius: theme.borderRadius.md,
    },

    // Product Grid Skeleton
    productGridSkeleton: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        padding: theme.spacing.md,
    },

    gridItemSkeleton: {
        marginBottom: theme.spacing.md,
    },

    // Categories Skeleton
    categoriesSkeleton: {
        flexDirection: 'row',
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
    },

    categorySkeleton: {
        alignItems: 'center',
        marginRight: theme.spacing.md,
    },

    categoryIconSkeleton: {
        marginBottom: theme.spacing.xs,
    },

    categoryTextSkeleton: {
        // Category text skeleton
    },

    // Product Details Skeleton
    productDetailsSkeleton: {
        backgroundColor: '#fff',
    },

    productMainImageSkeleton: {
        marginBottom: theme.spacing.lg,
    },

    productInfoSkeleton: {
        padding: theme.spacing.lg,
    },

    productTitleSkeleton: {
        marginBottom: theme.spacing.md,
    },

    productPriceDetailsSkeleton: {
        marginBottom: theme.spacing.lg,
    },

    productDescriptionSkeleton: {
        marginBottom: theme.spacing.xl,
    },

    productActionsSkeleton: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },

    actionButtonSkeleton: {
        borderRadius: theme.borderRadius.md,
    },

    // Loading Overlay
    overlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },

    transparentOverlay: {
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
    },

    overlayContent: {
        backgroundColor: '#fff',
        borderRadius: theme.borderRadius.lg,
        padding: theme.spacing.xl,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },

    overlayText: {
        marginTop: theme.spacing.md,
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.gray[700],
    },

    // Pull to Refresh
    pullToRefreshContainer: {
        flex: 1,
    },

    refreshIndicator: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: theme.spacing.md,
        backgroundColor: theme.colors.gray[50],
    },

    refreshText: {
        marginLeft: theme.spacing.sm,
        fontSize: theme.typography.fontSize.sm,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.gray[600],
    },
});
