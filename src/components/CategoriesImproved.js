import React, {useState, useEffect} from 'react';
import {
    View, 
    Text, 
    ScrollView, 
    Pressable, 
    Image, 
    StyleSheet,
    Dimensions
} from 'react-native';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faChevronRight} from '@fortawesome/free-solid-svg-icons';

import {observer} from 'mobx-react';
import {ProductStore} from '../store/product';
import {theme} from '../styles/theme';

const {width} = Dimensions.get('window');
const CATEGORY_WIDTH = (width - 60) / 2; // عرض كل فئة

export const CategoriesImproved = observer(() => {
    const {
        state: {categories, category},
        setCategory,
        getCategories,
    } = ProductStore;

    const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'horizontal'

    useEffect(() => {
        getCategories();
    }, [getCategories]);

    // عرض أفقي مثل التطبيق الحالي لكن محسن
    const renderHorizontalCategories = () => (
        <ScrollView 
            horizontal={true} 
            style={styles.horizontalContainer}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.horizontalContent}
        >
            <Pressable
                style={[
                    styles.horizontalCategory,
                    category === null && styles.horizontalCategorySelected
                ]}
                onPress={() => setCategory(null)}
            >
                <View style={styles.categoryIconContainer}>
                    <FontAwesomeIcon 
                        icon={faChevronRight} 
                        size={16} 
                        color={category === null ? '#fff' : theme.colors.primary[600]} 
                    />
                </View>
                <Text style={[
                    styles.horizontalCategoryText,
                    category === null && styles.horizontalCategoryTextSelected
                ]}>
                    جميع المنتجات
                </Text>
            </Pressable>
            
            {categories.map((cat, i) => (
                <Pressable
                    key={i}
                    style={[
                        styles.horizontalCategory,
                        category === cat.id && styles.horizontalCategorySelected
                    ]}
                    onPress={() => setCategory(cat.id)}
                >
                    <View style={styles.categoryIconContainer}>
                        {cat.image ? (
                            <Image 
                                source={{uri: cat.image}} 
                                style={styles.categoryIcon}
                            />
                        ) : (
                            <View style={styles.categoryIconPlaceholder}>
                                <Text style={styles.categoryIconText}>
                                    {cat.name.charAt(0)}
                                </Text>
                            </View>
                        )}
                    </View>
                    <Text style={[
                        styles.horizontalCategoryText,
                        category === cat.id && styles.horizontalCategoryTextSelected
                    ]}>
                        {cat.name}
                    </Text>
                    {cat.count && (
                        <Text style={styles.categoryCount}>
                            ({cat.count})
                        </Text>
                    )}
                </Pressable>
            ))}
        </ScrollView>
    );

    // عرض شبكي مثل أمازون
    const renderGridCategories = () => (
        <View style={styles.gridContainer}>
            <View style={styles.gridRow}>
                <Pressable
                    style={[
                        styles.gridCategory,
                        category === null && styles.gridCategorySelected
                    ]}
                    onPress={() => setCategory(null)}
                >
                    <View style={styles.gridCategoryImageContainer}>
                        <FontAwesomeIcon 
                            icon={faChevronRight} 
                            size={24} 
                            color={theme.colors.primary[600]} 
                        />
                    </View>
                    <Text style={styles.gridCategoryTitle}>جميع المنتجات</Text>
                    <Text style={styles.gridCategorySubtitle}>تصفح كل شيء</Text>
                </Pressable>
            </View>
            
            <View style={styles.gridContent}>
                {categories.map((cat, i) => (
                    <Pressable
                        key={i}
                        style={[
                            styles.gridCategory,
                            category === cat.id && styles.gridCategorySelected
                        ]}
                        onPress={() => setCategory(cat.id)}
                    >
                        <View style={styles.gridCategoryImageContainer}>
                            {cat.image ? (
                                <Image 
                                    source={{uri: cat.image}} 
                                    style={styles.gridCategoryImage}
                                />
                            ) : (
                                <View style={styles.gridCategoryImagePlaceholder}>
                                    <Text style={styles.gridCategoryImageText}>
                                        {cat.name.charAt(0)}
                                    </Text>
                                </View>
                            )}
                        </View>
                        <Text style={styles.gridCategoryTitle}>{cat.name}</Text>
                        <Text style={styles.gridCategorySubtitle}>
                            {cat.count ? `${cat.count} منتج` : 'تصفح المنتجات'}
                        </Text>
                    </Pressable>
                ))}
            </View>
        </View>
    );

    return (
        <View style={styles.container}>
            {viewMode === 'horizontal' ? renderHorizontalCategories() : renderGridCategories()}
        </View>
    );
});

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#fff',
        marginBottom: theme.spacing.md,
    },

    // أنماط العرض الأفقي المحسن
    horizontalContainer: {
        paddingVertical: theme.spacing.sm,
    },

    horizontalContent: {
        paddingHorizontal: theme.spacing.md,
        gap: theme.spacing.sm,
    },

    horizontalCategory: {
        alignItems: 'center',
        paddingVertical: theme.spacing.sm,
        paddingHorizontal: theme.spacing.md,
        backgroundColor: theme.colors.gray[50],
        borderRadius: theme.borderRadius.lg,
        borderWidth: 1,
        borderColor: theme.colors.gray[200],
        minWidth: 100,
    },

    horizontalCategorySelected: {
        backgroundColor: theme.colors.primary[600],
        borderColor: theme.colors.primary[600],
    },

    categoryIconContainer: {
        marginBottom: theme.spacing.xs,
    },

    categoryIcon: {
        width: 24,
        height: 24,
        borderRadius: 12,
    },

    categoryIconPlaceholder: {
        width: 24,
        height: 24,
        borderRadius: 12,
        backgroundColor: theme.colors.primary[100],
        alignItems: 'center',
        justifyContent: 'center',
    },

    categoryIconText: {
        fontSize: 12,
        fontWeight: 'bold',
        color: theme.colors.primary[600],
    },

    horizontalCategoryText: {
        fontSize: theme.typography.fontSize.sm,
        fontFamily: theme.typography.fontFamily.primarySemiBold,
        color: theme.colors.gray[700],
        textAlign: 'center',
    },

    horizontalCategoryTextSelected: {
        color: '#fff',
    },

    categoryCount: {
        fontSize: theme.typography.fontSize.xs,
        color: theme.colors.gray[500],
        marginTop: 2,
    },

    // أنماط العرض الشبكي
    gridContainer: {
        padding: theme.spacing.md,
    },

    gridRow: {
        marginBottom: theme.spacing.md,
    },

    gridContent: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
    },

    gridCategory: {
        width: CATEGORY_WIDTH,
        backgroundColor: '#fff',
        borderRadius: theme.borderRadius.lg,
        padding: theme.spacing.md,
        marginBottom: theme.spacing.md,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: theme.colors.gray[200],
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },

    gridCategorySelected: {
        borderColor: theme.colors.primary[600],
        borderWidth: 2,
    },

    gridCategoryImageContainer: {
        marginBottom: theme.spacing.sm,
    },

    gridCategoryImage: {
        width: 60,
        height: 60,
        borderRadius: 30,
    },

    gridCategoryImagePlaceholder: {
        width: 60,
        height: 60,
        borderRadius: 30,
        backgroundColor: theme.colors.primary[100],
        alignItems: 'center',
        justifyContent: 'center',
    },

    gridCategoryImageText: {
        fontSize: 24,
        fontWeight: 'bold',
        color: theme.colors.primary[600],
    },

    gridCategoryTitle: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.gray[900],
        textAlign: 'center',
        marginBottom: theme.spacing.xs,
    },

    gridCategorySubtitle: {
        fontSize: theme.typography.fontSize.sm,
        color: theme.colors.gray[600],
        textAlign: 'center',
    },
});
