import React, {useEffect, useState} from 'react';
import {
    View,
    Text,
    Image,
    ScrollView,
    Pressable,
    StyleSheet,
    Dimensions,
    FlatList
} from 'react-native';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
    faHeart,
    faShoppingCart,
    faStar,
    faStarHalfAlt,
    faEye,
    faGrid3x3,
    faList
} from '@fortawesome/free-solid-svg-icons';

import {ProductStore} from '../store/product';
import {observer} from 'mobx-react';
import {theme} from '../styles/theme';
import {Card, Button, Badge} from './ui';

const {width} = Dimensions.get('window');
const GRID_ITEM_WIDTH = (width - 45) / 2; // عرض كل منتج في الشبكة
const LIST_ITEM_WIDTH = width - 30; // عرض كل منتج في القائمة

export const ProductGridImproved = observer(({searchText, navigation, byCategory}) => {
    const {
        state: {searchedProducts, category, loading},
        setProduct,
        getSearchedProducts,
        getProductsByCategories,
    } = ProductStore;

    const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
    const [favorites, setFavorites] = useState(new Set());

    useEffect(() => {
        byCategory
            ? getProductsByCategories(category)
            : getSearchedProducts(searchText);
    }, [category, searchText]);

    // تبديل المفضلة
    const toggleFavorite = (productId) => {
        const newFavorites = new Set(favorites);
        if (newFavorites.has(productId)) {
            newFavorites.delete(productId);
        } else {
            newFavorites.add(productId);
        }
        setFavorites(newFavorites);
    };

    // عرض النجوم
    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;

        for (let i = 0; i < fullStars; i++) {
            stars.push(
                <FontAwesomeIcon
                    key={i}
                    icon={faStar}
                    size={12}
                    color={theme.colors.yellow[400]}
                />
            );
        }

        if (hasHalfStar) {
            stars.push(
                <FontAwesomeIcon
                    key="half"
                    icon={faStarHalfAlt}
                    size={12}
                    color={theme.colors.yellow[400]}
                />
            );
        }

        return stars;
    };

    // عرض منتج في الشبكة
    const renderGridItem = ({item}) => (
        <Card style={[styles.gridCard, {width: GRID_ITEM_WIDTH}]} padding="small">
            <Pressable
                onPress={() => {
                    setProduct(item);
                    navigation.navigate('Product');
                }}
                style={styles.productContainer}
            >
                {/* صورة المنتج */}
                <View style={styles.imageContainer}>
                    <Image
                        style={styles.gridImage}
                        source={{uri: item.imgs[0]}}
                        resizeMode="cover"
                    />
                    
                    {/* شارة الخصم */}
                    {item.discount && (
                        <Badge
                            variant="error"
                            style={styles.discountBadge}
                        >
                            -{item.discount}%
                        </Badge>
                    )}

                    {/* زر المفضلة */}
                    <Pressable
                        style={styles.favoriteButton}
                        onPress={() => toggleFavorite(item.id)}
                    >
                        <FontAwesomeIcon
                            icon={faHeart}
                            size={16}
                            color={favorites.has(item.id) ? theme.colors.red[500] : theme.colors.gray[400]}
                        />
                    </Pressable>
                </View>

                {/* معلومات المنتج */}
                <View style={styles.productInfo}>
                    <Text style={styles.gridProductName} numberOfLines={2}>
                        {item.name}
                    </Text>

                    {/* التقييم */}
                    {item.rating && (
                        <View style={styles.ratingContainer}>
                            <View style={styles.starsContainer}>
                                {renderStars(item.rating)}
                            </View>
                            <Text style={styles.ratingText}>
                                ({item.reviewCount || 0})
                            </Text>
                        </View>
                    )}

                    {/* السعر */}
                    <View style={styles.priceContainer}>
                        <Text style={styles.currentPrice}>
                            ${item.price}
                        </Text>
                        {item.originalPrice && (
                            <Text style={styles.originalPrice}>
                                ${item.originalPrice}
                            </Text>
                        )}
                    </View>

                    {/* حالة التوفر */}
                    <Text style={[
                        styles.stockStatus,
                        {color: item.inStock ? theme.colors.green[600] : theme.colors.red[600]}
                    ]}>
                        {item.inStock ? 'متوفر' : 'غير متوفر'}
                    </Text>
                </View>

                {/* زر الإضافة للسلة */}
                <Button
                    variant="primary"
                    size="small"
                    style={styles.addToCartButton}
                    disabled={!item.inStock}
                >
                    <FontAwesomeIcon icon={faShoppingCart} size={14} color="#fff" />
                </Button>
            </Pressable>
        </Card>
    );

    // عرض منتج في القائمة
    const renderListItem = ({item}) => (
        <Card style={[styles.listCard, {width: LIST_ITEM_WIDTH}]} padding="medium">
            <Pressable
                onPress={() => {
                    setProduct(item);
                    navigation.navigate('Product');
                }}
                style={styles.listContainer}
            >
                {/* صورة المنتج */}
                <View style={styles.listImageContainer}>
                    <Image
                        style={styles.listImage}
                        source={{uri: item.imgs[0]}}
                        resizeMode="cover"
                    />
                    {item.discount && (
                        <Badge variant="error" style={styles.listDiscountBadge}>
                            -{item.discount}%
                        </Badge>
                    )}
                </View>

                {/* معلومات المنتج */}
                <View style={styles.listProductInfo}>
                    <Text style={styles.listProductName} numberOfLines={2}>
                        {item.name}
                    </Text>

                    {item.rating && (
                        <View style={styles.ratingContainer}>
                            <View style={styles.starsContainer}>
                                {renderStars(item.rating)}
                            </View>
                            <Text style={styles.ratingText}>
                                ({item.reviewCount || 0})
                            </Text>
                        </View>
                    )}

                    <View style={styles.priceContainer}>
                        <Text style={styles.currentPrice}>${item.price}</Text>
                        {item.originalPrice && (
                            <Text style={styles.originalPrice}>${item.originalPrice}</Text>
                        )}
                    </View>

                    <Text style={[
                        styles.stockStatus,
                        {color: item.inStock ? theme.colors.green[600] : theme.colors.red[600]}
                    ]}>
                        {item.inStock ? 'متوفر' : 'غير متوفر'}
                    </Text>
                </View>

                {/* الأزرار */}
                <View style={styles.listActions}>
                    <Pressable
                        style={styles.listFavoriteButton}
                        onPress={() => toggleFavorite(item.id)}
                    >
                        <FontAwesomeIcon
                            icon={faHeart}
                            size={18}
                            color={favorites.has(item.id) ? theme.colors.red[500] : theme.colors.gray[400]}
                        />
                    </Pressable>

                    <Button
                        variant="primary"
                        size="small"
                        disabled={!item.inStock}
                    >
                        إضافة للسلة
                    </Button>
                </View>
            </Pressable>
        </Card>
    );

    return (
        <View style={styles.container}>
            {/* شريط التحكم في العرض */}
            <View style={styles.viewControls}>
                <View style={styles.viewModeButtons}>
                    <Pressable
                        style={[
                            styles.viewModeButton,
                            viewMode === 'grid' && styles.viewModeButtonActive
                        ]}
                        onPress={() => setViewMode('grid')}
                    >
                        <FontAwesomeIcon
                            icon={faGrid3x3}
                            size={16}
                            color={viewMode === 'grid' ? '#fff' : theme.colors.gray[600]}
                        />
                    </Pressable>
                    <Pressable
                        style={[
                            styles.viewModeButton,
                            viewMode === 'list' && styles.viewModeButtonActive
                        ]}
                        onPress={() => setViewMode('list')}
                    >
                        <FontAwesomeIcon
                            icon={faList}
                            size={16}
                            color={viewMode === 'list' ? '#fff' : theme.colors.gray[600]}
                        />
                    </Pressable>
                </View>

                <Text style={styles.resultsCount}>
                    {searchedProducts.length} منتج
                </Text>
            </View>

            {/* عرض النتائج */}
            {!byCategory && (
                <Text style={styles.searchText}>
                    {searchedProducts.length === 0 ? 'لا توجد' : ''} نتائج البحث عن "{searchText}"
                </Text>
            )}

            <FlatList
                data={searchedProducts}
                renderItem={viewMode === 'grid' ? renderGridItem : renderListItem}
                keyExtractor={(item, index) => `${item.id}-${index}`}
                numColumns={viewMode === 'grid' ? 2 : 1}
                key={viewMode} // إعادة تحديد الـ FlatList عند تغيير العرض
                contentContainerStyle={styles.listContainer}
                showsVerticalScrollIndicator={false}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
            />
        </View>
    );
});

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.gray[50],
    },

    viewControls: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        backgroundColor: '#fff',
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.gray[200],
    },

    viewModeButtons: {
        flexDirection: 'row',
        backgroundColor: theme.colors.gray[100],
        borderRadius: theme.borderRadius.md,
        padding: 2,
    },

    viewModeButton: {
        paddingHorizontal: theme.spacing.sm,
        paddingVertical: theme.spacing.xs,
        borderRadius: theme.borderRadius.sm,
    },

    viewModeButtonActive: {
        backgroundColor: theme.colors.primary[600],
    },

    resultsCount: {
        fontSize: theme.typography.fontSize.sm,
        color: theme.colors.gray[600],
        fontFamily: theme.typography.fontFamily.primary,
    },

    searchText: {
        fontFamily: theme.typography.fontFamily.primary,
        margin: theme.spacing.md,
        marginBottom: 0,
        fontSize: theme.typography.fontSize.md,
        color: theme.colors.gray[700],
    },

    listContainer: {
        padding: theme.spacing.sm,
    },

    separator: {
        height: theme.spacing.sm,
    },

    // أنماط عرض الشبكة
    gridCard: {
        marginHorizontal: theme.spacing.xs,
        marginBottom: theme.spacing.sm,
    },

    productContainer: {
        position: 'relative',
    },

    imageContainer: {
        position: 'relative',
        marginBottom: theme.spacing.sm,
    },

    gridImage: {
        width: '100%',
        height: 150,
        borderRadius: theme.borderRadius.md,
    },

    discountBadge: {
        position: 'absolute',
        top: theme.spacing.xs,
        left: theme.spacing.xs,
    },

    favoriteButton: {
        position: 'absolute',
        top: theme.spacing.xs,
        right: theme.spacing.xs,
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderRadius: 20,
        padding: theme.spacing.xs,
    },

    productInfo: {
        flex: 1,
    },

    gridProductName: {
        fontSize: theme.typography.fontSize.sm,
        fontFamily: theme.typography.fontFamily.primarySemiBold,
        color: theme.colors.gray[900],
        marginBottom: theme.spacing.xs,
        textAlign: 'right',
    },

    ratingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: theme.spacing.xs,
    },

    starsContainer: {
        flexDirection: 'row',
        marginRight: theme.spacing.xs,
    },

    ratingText: {
        fontSize: theme.typography.fontSize.xs,
        color: theme.colors.gray[500],
    },

    priceContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: theme.spacing.xs,
    },

    currentPrice: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.primary[600],
        marginRight: theme.spacing.xs,
    },

    originalPrice: {
        fontSize: theme.typography.fontSize.sm,
        color: theme.colors.gray[500],
        textDecorationLine: 'line-through',
    },

    stockStatus: {
        fontSize: theme.typography.fontSize.xs,
        fontFamily: theme.typography.fontFamily.primary,
        marginBottom: theme.spacing.sm,
    },

    addToCartButton: {
        alignSelf: 'center',
    },

    // أنماط عرض القائمة
    listCard: {
        marginBottom: theme.spacing.sm,
    },

    listContainer: {
        flexDirection: 'row',
        alignItems: 'flex-start',
    },

    listImageContainer: {
        position: 'relative',
        marginRight: theme.spacing.md,
    },

    listImage: {
        width: 100,
        height: 100,
        borderRadius: theme.borderRadius.md,
    },

    listDiscountBadge: {
        position: 'absolute',
        top: theme.spacing.xs,
        left: theme.spacing.xs,
    },

    listProductInfo: {
        flex: 1,
        paddingRight: theme.spacing.sm,
    },

    listProductName: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primarySemiBold,
        color: theme.colors.gray[900],
        marginBottom: theme.spacing.sm,
        textAlign: 'right',
    },

    listActions: {
        alignItems: 'center',
        justifyContent: 'space-between',
        height: 100,
    },

    listFavoriteButton: {
        padding: theme.spacing.sm,
        backgroundColor: theme.colors.gray[100],
        borderRadius: theme.borderRadius.full,
    },
});
