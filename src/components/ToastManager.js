import React, {useState, useEffect, useRef} from 'react';
import {
    View,
    Text,
    Animated,
    TouchableOpacity,
    StyleSheet,
    Dimensions,
    Platform
} from 'react-native';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
    faCheckCircle,
    faExclamationCircle,
    faInfoCircle,
    faTimesCircle,
    faTimes
} from '@fortawesome/free-solid-svg-icons';

import {theme} from '../styles/theme';

const {width} = Dimensions.get('window');

// أنواع Toast
const TOAST_TYPES = {
    success: {
        icon: faCheckCircle,
        color: theme.colors.green[500],
        backgroundColor: theme.colors.green[50],
        borderColor: theme.colors.green[200]
    },
    error: {
        icon: faTimesCircle,
        color: theme.colors.red[500],
        backgroundColor: theme.colors.red[50],
        borderColor: theme.colors.red[200]
    },
    warning: {
        icon: faExclamationCircle,
        color: theme.colors.yellow[500],
        backgroundColor: theme.colors.yellow[50],
        borderColor: theme.colors.yellow[200]
    },
    info: {
        icon: faInfoCircle,
        color: theme.colors.blue[500],
        backgroundColor: theme.colors.blue[50],
        borderColor: theme.colors.blue[200]
    }
};

// مكون Toast واحد
const ToastItem = ({toast, onDismiss}) => {
    const translateY = useRef(new Animated.Value(-100)).current;
    const opacity = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        // رسم متحرك للظهور
        Animated.parallel([
            Animated.timing(translateY, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            }),
            Animated.timing(opacity, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            })
        ]).start();

        // إخفاء تلقائي بعد المدة المحددة
        if (toast.duration > 0) {
            const timer = setTimeout(() => {
                handleDismiss();
            }, toast.duration);

            return () => clearTimeout(timer);
        }
    }, []);

    const handleDismiss = () => {
        Animated.parallel([
            Animated.timing(translateY, {
                toValue: -100,
                duration: 250,
                useNativeDriver: true,
            }),
            Animated.timing(opacity, {
                toValue: 0,
                duration: 250,
                useNativeDriver: true,
            })
        ]).start(() => {
            onDismiss(toast.id);
        });
    };

    const toastStyle = TOAST_TYPES[toast.type] || TOAST_TYPES.info;

    return (
        <Animated.View
            style={[
                styles.toastContainer,
                {
                    backgroundColor: toastStyle.backgroundColor,
                    borderColor: toastStyle.borderColor,
                    transform: [{translateY}],
                    opacity
                }
            ]}
        >
            <View style={styles.toastContent}>
                <FontAwesomeIcon
                    icon={toastStyle.icon}
                    size={20}
                    color={toastStyle.color}
                    style={styles.toastIcon}
                />
                
                <View style={styles.toastTextContainer}>
                    {toast.title && (
                        <Text style={[styles.toastTitle, {color: toastStyle.color}]}>
                            {toast.title}
                        </Text>
                    )}
                    <Text style={styles.toastMessage}>
                        {toast.message}
                    </Text>
                </View>

                {toast.dismissible && (
                    <TouchableOpacity
                        onPress={handleDismiss}
                        style={styles.dismissButton}
                        hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
                    >
                        <FontAwesomeIcon
                            icon={faTimes}
                            size={16}
                            color={theme.colors.gray[500]}
                        />
                    </TouchableOpacity>
                )}
            </View>

            {/* شريط التقدم للمدة */}
            {toast.duration > 0 && toast.showProgress && (
                <ToastProgressBar duration={toast.duration} color={toastStyle.color} />
            )}
        </Animated.View>
    );
};

// شريط التقدم
const ToastProgressBar = ({duration, color}) => {
    const progress = useRef(new Animated.Value(1)).current;

    useEffect(() => {
        Animated.timing(progress, {
            toValue: 0,
            duration: duration,
            useNativeDriver: false,
        }).start();
    }, [duration]);

    return (
        <View style={styles.progressBarContainer}>
            <Animated.View
                style={[
                    styles.progressBar,
                    {
                        backgroundColor: color,
                        width: progress.interpolate({
                            inputRange: [0, 1],
                            outputRange: ['0%', '100%']
                        })
                    }
                ]}
            />
        </View>
    );
};

// مدير Toast الرئيسي
class ToastManager {
    constructor() {
        this.toasts = [];
        this.listeners = [];
        this.nextId = 1;
    }

    // إضافة مستمع للتحديثات
    addListener(callback) {
        this.listeners.push(callback);
        return () => {
            this.listeners = this.listeners.filter(listener => listener !== callback);
        };
    }

    // إشعار جميع المستمعين
    notifyListeners() {
        this.listeners.forEach(callback => callback([...this.toasts]));
    }

    // إضافة toast جديد
    show(message, options = {}) {
        const toast = {
            id: this.nextId++,
            message,
            type: options.type || 'info',
            title: options.title,
            duration: options.duration !== undefined ? options.duration : 4000,
            dismissible: options.dismissible !== false,
            showProgress: options.showProgress !== false,
            ...options
        };

        this.toasts.push(toast);
        this.notifyListeners();

        return toast.id;
    }

    // إزالة toast
    dismiss(id) {
        this.toasts = this.toasts.filter(toast => toast.id !== id);
        this.notifyListeners();
    }

    // مسح جميع Toast
    clear() {
        this.toasts = [];
        this.notifyListeners();
    }

    // طرق مختصرة لأنواع مختلفة
    success(message, options = {}) {
        return this.show(message, {...options, type: 'success'});
    }

    error(message, options = {}) {
        return this.show(message, {...options, type: 'error', duration: 6000});
    }

    warning(message, options = {}) {
        return this.show(message, {...options, type: 'warning'});
    }

    info(message, options = {}) {
        return this.show(message, {...options, type: 'info'});
    }
}

// إنشاء instance واحد
export const toastManager = new ToastManager();

// مكون عرض Toast
const ToastContainer = () => {
    const [toasts, setToasts] = useState([]);

    useEffect(() => {
        const unsubscribe = toastManager.addListener(setToasts);
        return unsubscribe;
    }, []);

    if (toasts.length === 0) {
        return null;
    }

    return (
        <View style={styles.container} pointerEvents="box-none">
            {toasts.map(toast => (
                <ToastItem
                    key={toast.id}
                    toast={toast}
                    onDismiss={toastManager.dismiss.bind(toastManager)}
                />
            ))}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        top: Platform.OS === 'ios' ? 50 : 30,
        left: 0,
        right: 0,
        zIndex: 9999,
        paddingHorizontal: theme.spacing.md,
    },

    toastContainer: {
        marginBottom: theme.spacing.sm,
        borderRadius: theme.borderRadius.lg,
        borderWidth: 1,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
        overflow: 'hidden',
    },

    toastContent: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        padding: theme.spacing.md,
    },

    toastIcon: {
        marginRight: theme.spacing.sm,
        marginTop: 2,
    },

    toastTextContainer: {
        flex: 1,
    },

    toastTitle: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primarySemiBold,
        marginBottom: theme.spacing.xs,
        textAlign: 'right',
    },

    toastMessage: {
        fontSize: theme.typography.fontSize.sm,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.gray[700],
        lineHeight: 20,
        textAlign: 'right',
    },

    dismissButton: {
        padding: theme.spacing.xs,
        marginLeft: theme.spacing.sm,
    },

    progressBarContainer: {
        height: 3,
        backgroundColor: 'rgba(0, 0, 0, 0.1)',
    },

    progressBar: {
        height: '100%',
        borderRadius: 1.5,
    },
});

export default ToastContainer;
