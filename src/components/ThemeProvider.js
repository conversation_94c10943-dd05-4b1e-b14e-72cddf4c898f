import React, {createContext, useContext, useState, useEffect} from 'react';
import {Appearance, StatusBar} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {themes, getSystemTheme} from '../styles/theme';

// إنشاء Context للثيم
const ThemeContext = createContext();

// Hook لاستخدام الثيم
export const useTheme = () => {
    const context = useContext(ThemeContext);
    if (!context) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
};

// مزود الثيم
export const ThemeProvider = ({children}) => {
    const [currentTheme, setCurrentTheme] = useState('light');
    const [isSystemTheme, setIsSystemTheme] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    // تحميل إعدادات الثيم المحفوظة
    useEffect(() => {
        loadThemeSettings();
    }, []);

    // مراقبة تغييرات ثيم النظام
    useEffect(() => {
        if (isSystemTheme) {
            const subscription = Appearance.addChangeListener(({colorScheme}) => {
                const systemTheme = colorScheme === 'dark' ? 'dark' : 'light';
                setCurrentTheme(systemTheme);
                updateStatusBar(systemTheme);
            });

            return () => subscription?.remove();
        }
    }, [isSystemTheme]);

    // تحديث شريط الحالة عند تغيير الثيم
    useEffect(() => {
        updateStatusBar(currentTheme);
    }, [currentTheme]);

    const loadThemeSettings = async () => {
        try {
            const savedTheme = await AsyncStorage.getItem('app_theme');
            const savedIsSystemTheme = await AsyncStorage.getItem('app_use_system_theme');

            if (savedIsSystemTheme === 'true') {
                setIsSystemTheme(true);
                const systemTheme = getSystemTheme();
                setCurrentTheme(systemTheme);
            } else if (savedTheme && themes[savedTheme]) {
                setCurrentTheme(savedTheme);
                setIsSystemTheme(false);
            } else {
                // الإعدادات الافتراضية
                setCurrentTheme('light');
                setIsSystemTheme(false);
            }
        } catch (error) {
            console.error('Error loading theme settings:', error);
            setCurrentTheme('light');
            setIsSystemTheme(false);
        } finally {
            setIsLoading(false);
        }
    };

    const saveThemeSettings = async (theme, useSystemTheme) => {
        try {
            await AsyncStorage.setItem('app_theme', theme);
            await AsyncStorage.setItem('app_use_system_theme', useSystemTheme.toString());
        } catch (error) {
            console.error('Error saving theme settings:', error);
        }
    };

    const updateStatusBar = (themeName) => {
        const isDark = themeName === 'dark';
        StatusBar.setBarStyle(isDark ? 'light-content' : 'dark-content', true);
        
        // للأندرويد
        if (StatusBar.setBackgroundColor) {
            StatusBar.setBackgroundColor(
                themes[themeName].colors.surface,
                true
            );
        }
    };

    // تغيير الثيم يدوياً
    const setTheme = async (themeName) => {
        if (!themes[themeName]) {
            console.warn(`Theme "${themeName}" does not exist`);
            return;
        }

        setCurrentTheme(themeName);
        setIsSystemTheme(false);
        await saveThemeSettings(themeName, false);
    };

    // تفعيل/إلغاء تفعيل ثيم النظام
    const setSystemTheme = async (useSystemTheme) => {
        setIsSystemTheme(useSystemTheme);
        
        if (useSystemTheme) {
            const systemTheme = getSystemTheme();
            setCurrentTheme(systemTheme);
            await saveThemeSettings(systemTheme, true);
        } else {
            await saveThemeSettings(currentTheme, false);
        }
    };

    // تبديل الثيم
    const toggleTheme = async () => {
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        await setTheme(newTheme);
    };

    // الحصول على الثيم الحالي
    const getTheme = () => themes[currentTheme];

    // التحقق من كون الثيم داكن
    const isDarkTheme = () => currentTheme === 'dark';

    // قائمة الثيمات المتاحة
    const getAvailableThemes = () => Object.keys(themes);

    const contextValue = {
        // الحالة الحالية
        currentTheme,
        isSystemTheme,
        isLoading,
        
        // الثيم الحالي
        theme: getTheme(),
        
        // الوظائف
        setTheme,
        setSystemTheme,
        toggleTheme,
        getTheme,
        isDarkTheme,
        getAvailableThemes,
        
        // معلومات إضافية
        colors: getTheme().colors,
        typography: getTheme().typography,
        spacing: getTheme().spacing,
        borderRadius: getTheme().borderRadius,
        shadows: getTheme().shadows,
    };

    if (isLoading) {
        // يمكن إضافة شاشة تحميل هنا
        return null;
    }

    return (
        <ThemeContext.Provider value={contextValue}>
            {children}
        </ThemeContext.Provider>
    );
};

// Hook مخصص للألوان
export const useColors = () => {
    const {colors} = useTheme();
    return colors;
};

// Hook مخصص للخطوط
export const useTypography = () => {
    const {typography} = useTheme();
    return typography;
};

// Hook مخصص للمسافات
export const useSpacing = () => {
    const {spacing} = useTheme();
    return spacing;
};

// Hook مخصص للحدود
export const useBorderRadius = () => {
    const {borderRadius} = useTheme();
    return borderRadius;
};

// Hook مخصص للظلال
export const useShadows = () => {
    const {shadows} = useTheme();
    return shadows;
};

// مكون مساعد لتطبيق الثيم على المكونات
export const withTheme = (Component) => {
    return React.forwardRef((props, ref) => {
        const theme = useTheme();
        return <Component {...props} theme={theme} ref={ref} />;
    });
};

// Hook للحصول على أنماط محددة بناءً على الثيم
export const useThemedStyles = (styleCreator) => {
    const theme = useTheme();
    return React.useMemo(() => styleCreator(theme), [theme, styleCreator]);
};

// مكون مساعد لتطبيق الألوان التلقائية
export const ThemedView = ({style, ...props}) => {
    const {colors} = useTheme();
    return (
        <View
            style={[
                {backgroundColor: colors.background},
                style
            ]}
            {...props}
        />
    );
};

export const ThemedText = ({style, color, ...props}) => {
    const {colors, typography} = useTheme();
    return (
        <Text
            style={[
                {
                    color: color || colors.text,
                    fontFamily: typography.fontFamily.primary,
                    fontSize: typography.fontSize.md
                },
                style
            ]}
            {...props}
        />
    );
};

// مكون تبديل الثيم
export const ThemeToggle = ({style, ...props}) => {
    const {isDarkTheme, toggleTheme} = useTheme();
    
    return (
        <TouchableOpacity
            style={[
                {
                    padding: 12,
                    borderRadius: 8,
                    backgroundColor: isDarkTheme() ? '#374151' : '#f3f4f6'
                },
                style
            ]}
            onPress={toggleTheme}
            {...props}
        >
            <FontAwesomeIcon
                icon={isDarkTheme() ? faSun : faMoon}
                size={20}
                color={isDarkTheme() ? '#fbbf24' : '#374151'}
            />
        </TouchableOpacity>
    );
};

export default ThemeProvider;
