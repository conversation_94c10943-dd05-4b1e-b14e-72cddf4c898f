import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet, ScrollView} from 'react-native';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
    faExclamationTriangle,
    faRefresh,
    faBug,
    faHome
} from '@fortawesome/free-solid-svg-icons';

import {Card, Button} from './ui';
import {theme} from '../styles/theme';

class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            hasError: false,
            error: null,
            errorInfo: null,
            errorId: null
        };
    }

    static getDerivedStateFromError(error) {
        // تحديث الحالة لإظهار واجهة الخطأ
        return {
            hasError: true,
            errorId: Math.random().toString(36).substr(2, 9)
        };
    }

    componentDidCatch(error, errorInfo) {
        // تسجيل تفاصيل الخطأ
        console.error('ErrorBoundary caught an error:', error, errorInfo);
        
        this.setState({
            error: error,
            errorInfo: errorInfo
        });

        // إرسال الخطأ لخدمة التتبع (مثل Sentry)
        this.logErrorToService(error, errorInfo);
    }

    logErrorToService = (error, errorInfo) => {
        // يمكن إضافة خدمة تتبع الأخطاء هنا
        const errorData = {
            message: error.message,
            stack: error.stack,
            componentStack: errorInfo.componentStack,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location?.href,
            errorId: this.state.errorId
        };

        // مثال: إرسال للخادم
        // fetch('/api/errors', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify(errorData)
        // });

        console.log('Error logged:', errorData);
    };

    handleRetry = () => {
        this.setState({
            hasError: false,
            error: null,
            errorInfo: null,
            errorId: null
        });
    };

    handleGoHome = () => {
        this.setState({
            hasError: false,
            error: null,
            errorInfo: null,
            errorId: null
        });
        
        // التنقل للصفحة الرئيسية
        if (this.props.navigation) {
            this.props.navigation.navigate('Dashboard');
        }
    };

    render() {
        if (this.state.hasError) {
            return (
                <View style={styles.container}>
                    <ScrollView 
                        contentContainerStyle={styles.scrollContent}
                        showsVerticalScrollIndicator={false}
                    >
                        <Card style={styles.errorCard} padding="large">
                            {/* أيقونة الخطأ */}
                            <View style={styles.iconContainer}>
                                <FontAwesomeIcon
                                    icon={faExclamationTriangle}
                                    size={60}
                                    color={theme.colors.red[500]}
                                />
                            </View>

                            {/* عنوان الخطأ */}
                            <Text style={styles.errorTitle}>
                                عذراً، حدث خطأ غير متوقع
                            </Text>

                            {/* وصف الخطأ */}
                            <Text style={styles.errorDescription}>
                                واجه التطبيق مشكلة تقنية. نحن نعمل على حل هذه المشكلة.
                            </Text>

                            {/* معرف الخطأ */}
                            <View style={styles.errorIdContainer}>
                                <FontAwesomeIcon
                                    icon={faBug}
                                    size={14}
                                    color={theme.colors.gray[500]}
                                />
                                <Text style={styles.errorId}>
                                    معرف الخطأ: {this.state.errorId}
                                </Text>
                            </View>

                            {/* تفاصيل الخطأ (في وضع التطوير فقط) */}
                            {__DEV__ && this.state.error && (
                                <Card style={styles.errorDetailsCard} padding="medium">
                                    <Text style={styles.errorDetailsTitle}>
                                        تفاصيل الخطأ (وضع التطوير):
                                    </Text>
                                    <Text style={styles.errorMessage}>
                                        {this.state.error.message}
                                    </Text>
                                    {this.state.error.stack && (
                                        <ScrollView 
                                            style={styles.stackTraceContainer}
                                            horizontal={true}
                                        >
                                            <Text style={styles.stackTrace}>
                                                {this.state.error.stack}
                                            </Text>
                                        </ScrollView>
                                    )}
                                </Card>
                            )}

                            {/* أزرار الإجراءات */}
                            <View style={styles.actionsContainer}>
                                <Button
                                    variant="primary"
                                    onPress={this.handleRetry}
                                    style={styles.retryButton}
                                >
                                    <FontAwesomeIcon
                                        icon={faRefresh}
                                        size={16}
                                        color="#fff"
                                        style={styles.buttonIcon}
                                    />
                                    <Text style={styles.buttonText}>
                                        إعادة المحاولة
                                    </Text>
                                </Button>

                                <Button
                                    variant="outline"
                                    onPress={this.handleGoHome}
                                    style={styles.homeButton}
                                >
                                    <FontAwesomeIcon
                                        icon={faHome}
                                        size={16}
                                        color={theme.colors.primary[600]}
                                        style={styles.buttonIcon}
                                    />
                                    <Text style={[styles.buttonText, styles.homeButtonText]}>
                                        العودة للرئيسية
                                    </Text>
                                </Button>
                            </View>

                            {/* نصائح للمستخدم */}
                            <Card style={styles.tipsCard} padding="medium">
                                <Text style={styles.tipsTitle}>
                                    نصائح لحل المشكلة:
                                </Text>
                                <View style={styles.tipsList}>
                                    <Text style={styles.tipItem}>
                                        • تأكد من اتصالك بالإنترنت
                                    </Text>
                                    <Text style={styles.tipItem}>
                                        • أعد تشغيل التطبيق
                                    </Text>
                                    <Text style={styles.tipItem}>
                                        • تحديث التطبيق لآخر إصدار
                                    </Text>
                                    <Text style={styles.tipItem}>
                                        • تواصل مع الدعم الفني إذا استمرت المشكلة
                                    </Text>
                                </View>
                            </Card>
                        </Card>
                    </ScrollView>
                </View>
            );
        }

        return this.props.children;
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.gray[50],
    },

    scrollContent: {
        flexGrow: 1,
        justifyContent: 'center',
        padding: theme.spacing.lg,
    },

    errorCard: {
        alignItems: 'center',
        backgroundColor: '#fff',
    },

    iconContainer: {
        marginBottom: theme.spacing.lg,
    },

    errorTitle: {
        fontSize: theme.typography.fontSize.xl,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.gray[900],
        textAlign: 'center',
        marginBottom: theme.spacing.md,
    },

    errorDescription: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.gray[600],
        textAlign: 'center',
        lineHeight: 24,
        marginBottom: theme.spacing.lg,
    },

    errorIdContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: theme.spacing.lg,
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        backgroundColor: theme.colors.gray[100],
        borderRadius: theme.borderRadius.md,
    },

    errorId: {
        fontSize: theme.typography.fontSize.sm,
        fontFamily: theme.typography.fontFamily.mono,
        color: theme.colors.gray[600],
        marginLeft: theme.spacing.sm,
    },

    errorDetailsCard: {
        width: '100%',
        backgroundColor: theme.colors.red[50],
        borderColor: theme.colors.red[200],
        borderWidth: 1,
        marginBottom: theme.spacing.lg,
    },

    errorDetailsTitle: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primarySemiBold,
        color: theme.colors.red[700],
        marginBottom: theme.spacing.sm,
    },

    errorMessage: {
        fontSize: theme.typography.fontSize.sm,
        fontFamily: theme.typography.fontFamily.mono,
        color: theme.colors.red[600],
        marginBottom: theme.spacing.sm,
    },

    stackTraceContainer: {
        maxHeight: 150,
        backgroundColor: theme.colors.gray[900],
        borderRadius: theme.borderRadius.sm,
        padding: theme.spacing.sm,
    },

    stackTrace: {
        fontSize: theme.typography.fontSize.xs,
        fontFamily: theme.typography.fontFamily.mono,
        color: theme.colors.green[400],
        lineHeight: 16,
    },

    actionsContainer: {
        width: '100%',
        gap: theme.spacing.md,
        marginBottom: theme.spacing.lg,
    },

    retryButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },

    homeButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },

    buttonIcon: {
        marginRight: theme.spacing.sm,
    },

    buttonText: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primarySemiBold,
        color: '#fff',
    },

    homeButtonText: {
        color: theme.colors.primary[600],
    },

    tipsCard: {
        width: '100%',
        backgroundColor: theme.colors.blue[50],
        borderColor: theme.colors.blue[200],
        borderWidth: 1,
    },

    tipsTitle: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primarySemiBold,
        color: theme.colors.blue[700],
        marginBottom: theme.spacing.sm,
    },

    tipsList: {
        gap: theme.spacing.xs,
    },

    tipItem: {
        fontSize: theme.typography.fontSize.sm,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.blue[600],
        lineHeight: 20,
    },
});

export default ErrorBoundary;
