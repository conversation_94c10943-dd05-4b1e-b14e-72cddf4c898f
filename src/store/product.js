import {makeObservable, observable, action, runInAction, computed} from 'mobx';
import {ToastAndroid, Platform} from 'react-native';

import {ecommerceService, vehiclePartsService} from '../services/apiService';
import {categories} from '../data/categories';
import {products} from '../data/products';
import {cart} from '../data/cart';
import {wishlist} from '../data/wishlist';

// نظام إدارة الأخطاء المحسن
class ErrorManager {
    static createError(type, message, details = null) {
        return {
            type,
            message,
            details,
            timestamp: Date.now(),
            id: Math.random().toString(36).substr(2, 9)
        };
    }

    static getErrorMessage(error) {
        if (typeof error === 'string') return error;
        if (error?.response?.data?.message) return error.response.data.message;
        if (error?.message) return error.message;
        return 'حدث خطأ غير متوقع';
    }
}

// نظام إدارة التحميل المحسن
class LoadingManager {
    constructor() {
        this.loadingStates = new Map();
        makeObservable(this, {
            loadingStates: observable,
            setLoading: action,
            isLoading: computed,
            getLoadingState: action,
        });
    }

    setLoading(key, isLoading) {
        this.loadingStates.set(key, isLoading);
    }

    get isLoading() {
        return Array.from(this.loadingStates.values()).some(loading => loading);
    }

    getLoadingState(key) {
        return this.loadingStates.get(key) || false;
    }

    clearAll() {
        this.loadingStates.clear();
    }
}

// نظام Cache بسيط
class CacheManager {
    constructor() {
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 دقائق
    }

    set(key, data) {
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    get(key) {
        const cached = this.cache.get(key);
        if (!cached) return null;
        
        if (Date.now() - cached.timestamp > this.cacheTimeout) {
            this.cache.delete(key);
            return null;
        }
        
        return cached.data;
    }

    clear() {
        this.cache.clear();
    }

    delete(key) {
        this.cache.delete(key);
    }
}

class ProductStoreImproved {
    constructor() {
        // إدارة الحالة الأساسية
        this.state = {
            // المنتجات
            allProducts: products,
            searchedProducts: [],
            products: [],
            product: {},
            
            // الفئات والعلامات التجارية
            categories: [],
            brands: [],
            category: null,
            
            // السلة والمفضلة
            cart: cart,
            wishlist: wishlist,
            
            // قطع غيار المركبات
            vehicleCategories: [],
            makes: [],
            models: [],
            selectedMake: null,
            selectedModel: null,
            
            // البحث والفلاتر
            searchHistory: [],
            filters: {
                priceRange: [0, 1000],
                brands: [],
                rating: 0,
                inStock: false
            },
            sortBy: 'relevance',
            
            // الأخطاء
            errors: [],
            lastError: null,
        };

        // المدراء المساعدون
        this.loadingManager = new LoadingManager();
        this.cacheManager = new CacheManager();
        
        // إعداد MobX
        makeObservable(this, {
            state: observable,
            
            // Actions الأساسية
            setCategory: action,
            setProduct: action,
            setSortBy: action,
            setFilters: action,
            addError: action,
            clearErrors: action,
            clearError: action,
            
            // Actions المنتجات
            getCategories: action,
            getProducts: action,
            getProductDetails: action,
            getProductsByCategories: action,
            getSearchedProducts: action,
            getBrands: action,
            
            // Actions السلة والمفضلة
            addToCart: action,
            removeFromCart: action,
            updateCartQuantity: action,
            clearCart: action,
            addToWishlist: action,
            removeFromWishlist: action,
            
            // Actions قطع غيار المركبات
            getVehicleCategories: action,
            getVehicleMakes: action,
            getVehicleModels: action,
            searchVehicleParts: action,
            
            // Computed values
            cartTotal: computed,
            cartItemsCount: computed,
            wishlistCount: computed,
            hasErrors: computed,
            isLoading: computed,
        });
    }

    // =============== Computed Values ===============
    get cartTotal() {
        return this.state.cart.reduce((total, item) => {
            return total + (item.product.price * item.quantity);
        }, 0);
    }

    get cartItemsCount() {
        return this.state.cart.reduce((count, item) => count + item.quantity, 0);
    }

    get wishlistCount() {
        return this.state.wishlist.length;
    }

    get hasErrors() {
        return this.state.errors.length > 0;
    }

    get isLoading() {
        return this.loadingManager.isLoading;
    }

    // =============== Utility Methods ===============
    createToast = (message, type = 'info') => {
        if (Platform.OS === 'android') {
            ToastAndroid.showWithGravityAndOffset(
                message,
                ToastAndroid.LONG,
                ToastAndroid.BOTTOM,
                0,
                50,
            );
        }
        // يمكن إضافة Toast للـ iOS هنا
    };

    shuffle = (array) => {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    };

    // =============== Error Management ===============
    addError = (error) => {
        const errorObj = ErrorManager.createError(
            error.type || 'general',
            ErrorManager.getErrorMessage(error),
            error.details
        );
        
        runInAction(() => {
            this.state.errors.push(errorObj);
            this.state.lastError = errorObj;
        });
        
        // عرض Toast للخطأ
        this.createToast(errorObj.message, 'error');
        
        return errorObj;
    };

    clearErrors = () => {
        runInAction(() => {
            this.state.errors = [];
            this.state.lastError = null;
        });
    };

    clearError = (errorId) => {
        runInAction(() => {
            this.state.errors = this.state.errors.filter(e => e.id !== errorId);
            if (this.state.lastError?.id === errorId) {
                this.state.lastError = this.state.errors[this.state.errors.length - 1] || null;
            }
        });
    };

    // =============== Basic Actions ===============
    setCategory = (id) => {
        runInAction(() => {
            this.state.category = id;
        });
    };

    setProduct = (data) => {
        runInAction(() => {
            this.state.product = data;
        });
    };

    setSortBy = (sortBy) => {
        runInAction(() => {
            this.state.sortBy = sortBy;
        });
    };

    setFilters = (filters) => {
        runInAction(() => {
            this.state.filters = { ...this.state.filters, ...filters };
        });
    };

    // =============== API Methods ===============
    async executeWithErrorHandling(operation, loadingKey, fallbackData = null) {
        try {
            this.loadingManager.setLoading(loadingKey, true);
            this.clearErrors();
            
            const result = await operation();
            return { success: true, data: result };
            
        } catch (error) {
            console.error(`${loadingKey} error:`, error);
            
            const errorObj = this.addError({
                type: loadingKey,
                message: ErrorManager.getErrorMessage(error),
                details: error
            });
            
            // استخدام البيانات الاحتياطية إذا توفرت
            if (fallbackData) {
                return { success: false, error: errorObj, fallbackUsed: true, data: fallbackData };
            }
            
            return { success: false, error: errorObj };
            
        } finally {
            this.loadingManager.setLoading(loadingKey, false);
        }
    }

    // =============== Categories ===============
    getCategories = async (useApi = true, forceRefresh = false) => {
        const cacheKey = 'categories';
        
        // التحقق من الـ Cache أولاً
        if (!forceRefresh && useApi) {
            const cached = this.cacheManager.get(cacheKey);
            if (cached) {
                runInAction(() => {
                    this.state.categories = cached;
                });
                return { success: true, data: cached, fromCache: true };
            }
        }

        if (!useApi) {
            runInAction(() => {
                this.state.categories = categories;
            });
            return { success: true, data: categories };
        }

        return await this.executeWithErrorHandling(
            async () => {
                const response = await ecommerceService.getCategories();
                
                if (response.error === false && response.data) {
                    const categoriesData = response.data.data || response.data;
                    
                    runInAction(() => {
                        this.state.categories = categoriesData;
                    });
                    
                    // حفظ في الـ Cache
                    this.cacheManager.set(cacheKey, categoriesData);
                    
                    return categoriesData;
                } else {
                    throw new Error('Invalid response format');
                }
            },
            'getCategories',
            categories // البيانات الاحتياطية
        );
    };

    // =============== Products ===============
    getProducts = async (params = {}, useApi = true, forceRefresh = false) => {
        const cacheKey = `products_${JSON.stringify(params)}`;
        
        // التحقق من الـ Cache
        if (!forceRefresh && useApi) {
            const cached = this.cacheManager.get(cacheKey);
            if (cached) {
                runInAction(() => {
                    this.state.products = cached;
                    this.state.allProducts = cached;
                });
                return { success: true, data: cached, fromCache: true };
            }
        }

        if (!useApi) {
            const shuffledProducts = this.shuffle(products);
            runInAction(() => {
                this.state.products = shuffledProducts;
                this.state.allProducts = shuffledProducts;
            });
            return { success: true, data: shuffledProducts };
        }

        return await this.executeWithErrorHandling(
            async () => {
                const response = await ecommerceService.getProducts(params);
                
                if (response.error === false && response.data) {
                    const productsData = response.data.data || response.data;
                    
                    runInAction(() => {
                        this.state.products = productsData;
                        this.state.allProducts = productsData;
                    });
                    
                    // حفظ في الـ Cache
                    this.cacheManager.set(cacheKey, productsData);
                    
                    return productsData;
                } else {
                    throw new Error('Invalid response format');
                }
            },
            'getProducts',
            this.shuffle(products) // البيانات الاحتياطية
        );
    };

    // =============== Product Details ===============
    getProductDetails = async (slug, forceRefresh = false) => {
        const cacheKey = `product_${slug}`;

        if (!forceRefresh) {
            const cached = this.cacheManager.get(cacheKey);
            if (cached) {
                runInAction(() => {
                    this.state.product = cached;
                });
                return { success: true, data: cached, fromCache: true };
            }
        }

        return await this.executeWithErrorHandling(
            async () => {
                const response = await ecommerceService.getProductDetails(slug);

                if (response.error === false && response.data) {
                    runInAction(() => {
                        this.state.product = response.data;
                    });

                    this.cacheManager.set(cacheKey, response.data);
                    return response.data;
                } else {
                    throw new Error('Product not found');
                }
            },
            'getProductDetails'
        );
    };

    // =============== Search Products ===============
    getSearchedProducts = async (text, useApi = true, forceRefresh = false) => {
        if (!text || text.trim().length === 0) {
            runInAction(() => {
                this.state.searchedProducts = [];
            });
            return { success: true, data: [] };
        }

        const cacheKey = `search_${text.toLowerCase()}`;

        if (!forceRefresh && useApi) {
            const cached = this.cacheManager.get(cacheKey);
            if (cached) {
                runInAction(() => {
                    this.state.searchedProducts = cached;
                });
                return { success: true, data: cached, fromCache: true };
            }
        }

        // إضافة للتاريخ
        this.addToSearchHistory(text);

        if (!useApi) {
            const filtered = this.state.allProducts.filter(x =>
                x.name.toLowerCase().includes(text.toLowerCase())
            );
            const shuffled = this.shuffle(filtered);

            runInAction(() => {
                this.state.searchedProducts = shuffled;
            });
            return { success: true, data: shuffled };
        }

        return await this.executeWithErrorHandling(
            async () => {
                const response = await ecommerceService.getProducts({ search: text });

                if (response.error === false && response.data) {
                    const searchResults = response.data.data || response.data;

                    runInAction(() => {
                        this.state.searchedProducts = searchResults;
                    });

                    this.cacheManager.set(cacheKey, searchResults);
                    return searchResults;
                } else {
                    throw new Error('Search failed');
                }
            },
            'getSearchedProducts',
            this.state.allProducts.filter(x =>
                x.name.toLowerCase().includes(text.toLowerCase())
            )
        );
    };

    // =============== Products by Category ===============
    getProductsByCategories = async (id, useApi = true, forceRefresh = false) => {
        const cacheKey = `category_products_${id || 'all'}`;

        if (!forceRefresh && useApi) {
            const cached = this.cacheManager.get(cacheKey);
            if (cached) {
                runInAction(() => {
                    this.state.searchedProducts = cached;
                });
                return { success: true, data: cached, fromCache: true };
            }
        }

        if (!useApi) {
            let filtered;
            if (id === null) {
                filtered = this.shuffle(this.state.allProducts);
            } else {
                filtered = this.shuffle(
                    this.state.allProducts.filter(x => x.category === id)
                );
            }

            runInAction(() => {
                this.state.searchedProducts = filtered;
            });
            return { success: true, data: filtered };
        }

        return await this.executeWithErrorHandling(
            async () => {
                let response;
                if (id === null) {
                    response = await ecommerceService.getProducts();
                } else {
                    response = await ecommerceService.getCategoryProducts(id);
                }

                if (response.error === false && response.data) {
                    const productsData = response.data.data || response.data;

                    runInAction(() => {
                        this.state.searchedProducts = productsData;
                    });

                    this.cacheManager.set(cacheKey, productsData);
                    return productsData;
                } else {
                    throw new Error('Failed to get category products');
                }
            },
            'getProductsByCategories',
            id === null
                ? this.shuffle(this.state.allProducts)
                : this.shuffle(this.state.allProducts.filter(x => x.category === id))
        );
    };

    // =============== Search History ===============
    addToSearchHistory = (searchTerm) => {
        const trimmed = searchTerm.trim();
        if (!trimmed) return;

        runInAction(() => {
            // إزالة المصطلح إذا كان موجوداً مسبقاً
            this.state.searchHistory = this.state.searchHistory.filter(
                term => term.toLowerCase() !== trimmed.toLowerCase()
            );

            // إضافة في المقدمة
            this.state.searchHistory.unshift(trimmed);

            // الاحتفاظ بآخر 10 عمليات بحث فقط
            if (this.state.searchHistory.length > 10) {
                this.state.searchHistory = this.state.searchHistory.slice(0, 10);
            }
        });
    };

    clearSearchHistory = () => {
        runInAction(() => {
            this.state.searchHistory = [];
        });
    };

    // =============== Cart Management ===============
    addToCart = (product, quantity = 1) => {
        const existingItem = this.state.cart.find(x => x.product.id === product.id);

        runInAction(() => {
            if (existingItem) {
                existingItem.quantity += quantity;
                this.createToast('تم تحديث الكمية في السلة', 'success');
            } else {
                this.state.cart.push({
                    product: product,
                    quantity: quantity
                });
                this.createToast('تم إضافة المنتج للسلة', 'success');
            }
        });
    };

    removeFromCart = (productId) => {
        runInAction(() => {
            this.state.cart = this.state.cart.filter(x => x.product.id !== productId);
        });
        this.createToast('تم حذف المنتج من السلة', 'info');
    };

    updateCartQuantity = (productId, quantity) => {
        if (quantity <= 0) {
            this.removeFromCart(productId);
            return;
        }

        runInAction(() => {
            const item = this.state.cart.find(x => x.product.id === productId);
            if (item) {
                item.quantity = quantity;
            }
        });
    };

    clearCart = () => {
        runInAction(() => {
            this.state.cart = [];
        });
        this.createToast('تم مسح السلة', 'info');
    };

    // =============== Wishlist Management ===============
    addToWishlist = (product) => {
        const exists = this.state.wishlist.find(x => x.id === product.id);

        if (exists) {
            this.createToast('المنتج موجود في المفضلة مسبقاً', 'warning');
            return;
        }

        runInAction(() => {
            this.state.wishlist.push(product);
        });
        this.createToast('تم إضافة المنتج للمفضلة', 'success');
    };

    removeFromWishlist = (productId) => {
        runInAction(() => {
            this.state.wishlist = this.state.wishlist.filter(x => x.id !== productId);
        });
        this.createToast('تم حذف المنتج من المفضلة', 'info');
    };

    // =============== Utility Methods ===============
    getRandomProducts = (count = 6) => {
        return this.shuffle(this.state.allProducts).slice(0, count);
    };

    // =============== Cache Management ===============
    clearCache = () => {
        this.cacheManager.clear();
    };

    refreshData = async () => {
        this.clearCache();
        await Promise.all([
            this.getCategories(true, true),
            this.getProducts({}, true, true)
        ]);
    };
}

// إنشاء instance واحد
export const ProductStoreImproved = new ProductStoreImproved();
