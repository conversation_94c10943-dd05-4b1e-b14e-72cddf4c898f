/**
 * فحص شامل لتطبيق دليل كار
 * Comprehensive Test for Dalila Car App
 */

console.log('🔍 بدء الفحص الشامل لتطبيق دليل كار...');

// متغيرات الفحص
let testResults = {
    api: { passed: 0, failed: 0, issues: [] },
    ui: { passed: 0, failed: 0, issues: [] },
    search: { passed: 0, failed: 0, issues: [] },
    navigation: { passed: 0, failed: 0, issues: [] },
    cart: { passed: 0, failed: 0, issues: [] },
    performance: { passed: 0, failed: 0, issues: [] }
};

// 1. فحص الاتصال بـ API
async function testAPIConnection() {
    console.log('📡 فحص الاتصال بـ API...');
    
    try {
        // فحص جلب المنتجات
        const response = await fetch('https://dalilakauto.com/api/v1/ecommerce/products?limit=5');
        if (response.ok) {
            const data = await response.json();
            if (data.data && data.data.length > 0) {
                testResults.api.passed++;
                console.log('✅ API المنتجات يعمل بشكل صحيح');
            } else {
                testResults.api.failed++;
                testResults.api.issues.push('API يعيد بيانات فارغة');
            }
        } else {
            testResults.api.failed++;
            testResults.api.issues.push(`API خطأ: ${response.status}`);
        }
    } catch (error) {
        testResults.api.failed++;
        testResults.api.issues.push(`خطأ في الاتصال: ${error.message}`);
    }

    // فحص جلب الفئات
    try {
        const response = await fetch('https://dalilakauto.com/api/v1/ecommerce/product-categories');
        if (response.ok) {
            testResults.api.passed++;
            console.log('✅ API الفئات يعمل بشكل صحيح');
        } else {
            testResults.api.failed++;
            testResults.api.issues.push(`API الفئات خطأ: ${response.status}`);
        }
    } catch (error) {
        testResults.api.failed++;
        testResults.api.issues.push(`خطأ في API الفئات: ${error.message}`);
    }
}

// 2. فحص عناصر واجهة المستخدم
function testUIElements() {
    console.log('🎨 فحص عناصر واجهة المستخدم...');
    
    const requiredElements = [
        'searchInput',
        'cartBadge',
        'wishlistBadge',
        'mainContent',
        'searchPage',
        'cartPage',
        'profilePage'
    ];

    requiredElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            testResults.ui.passed++;
            console.log(`✅ العنصر ${elementId} موجود`);
        } else {
            testResults.ui.failed++;
            testResults.ui.issues.push(`العنصر ${elementId} مفقود`);
            console.log(`❌ العنصر ${elementId} مفقود`);
        }
    });

    // فحص التصميم المتجاوب
    const viewport = window.innerWidth;
    if (viewport < 768) {
        console.log('📱 فحص التصميم للهاتف المحمول');
    } else if (viewport < 1024) {
        console.log('📱 فحص التصميم للتابلت');
    } else {
        console.log('💻 فحص التصميم لسطح المكتب');
    }
}

// 3. فحص وظائف البحث
function testSearchFunctionality() {
    console.log('🔍 فحص وظائف البحث...');
    
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        // محاكاة البحث
        searchInput.value = 'هيونداي';
        searchInput.dispatchEvent(new Event('input'));
        
        setTimeout(() => {
            const searchResults = document.querySelectorAll('.product-card');
            if (searchResults.length > 0) {
                testResults.search.passed++;
                console.log(`✅ البحث يعمل - تم العثور على ${searchResults.length} نتيجة`);
            } else {
                testResults.search.failed++;
                testResults.search.issues.push('البحث لا يعيد نتائج');
            }
        }, 1000);
    } else {
        testResults.search.failed++;
        testResults.search.issues.push('حقل البحث غير موجود');
    }
}

// 4. فحص التنقل
function testNavigation() {
    console.log('🧭 فحص التنقل...');
    
    const pages = ['home', 'search', 'cart', 'profile'];
    
    pages.forEach(page => {
        try {
            if (typeof showPage === 'function') {
                showPage(page);
                testResults.navigation.passed++;
                console.log(`✅ التنقل إلى ${page} يعمل`);
            } else {
                testResults.navigation.failed++;
                testResults.navigation.issues.push('دالة showPage غير موجودة');
            }
        } catch (error) {
            testResults.navigation.failed++;
            testResults.navigation.issues.push(`خطأ في التنقل إلى ${page}: ${error.message}`);
        }
    });
}

// 5. فحص سلة التسوق
function testCartFunctionality() {
    console.log('🛒 فحص سلة التسوق...');
    
    try {
        // محاكاة إضافة منتج للسلة
        const testProduct = {
            id: 1,
            name: 'منتج تجريبي',
            price: 100,
            image: 'test.jpg'
        };
        
        if (typeof addToCart === 'function') {
            addToCart(testProduct);
            
            // فحص إذا تم إضافة المنتج
            const cart = JSON.parse(localStorage.getItem('cart') || '[]');
            if (cart.length > 0) {
                testResults.cart.passed++;
                console.log('✅ إضافة المنتج للسلة يعمل');
                
                // تنظيف السلة
                localStorage.setItem('cart', '[]');
            } else {
                testResults.cart.failed++;
                testResults.cart.issues.push('فشل في إضافة المنتج للسلة');
            }
        } else {
            testResults.cart.failed++;
            testResults.cart.issues.push('دالة addToCart غير موجودة');
        }
    } catch (error) {
        testResults.cart.failed++;
        testResults.cart.issues.push(`خطأ في سلة التسوق: ${error.message}`);
    }
}

// 6. فحص الأداء
function testPerformance() {
    console.log('⚡ فحص الأداء...');
    
    // فحص سرعة التحميل
    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
    if (loadTime < 5000) {
        testResults.performance.passed++;
        console.log(`✅ سرعة التحميل جيدة: ${loadTime}ms`);
    } else {
        testResults.performance.failed++;
        testResults.performance.issues.push(`سرعة التحميل بطيئة: ${loadTime}ms`);
    }
    
    // فحص استخدام الذاكرة
    if (performance.memory) {
        const memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
        if (memoryUsage < 50) {
            testResults.performance.passed++;
            console.log(`✅ استخدام الذاكرة جيد: ${memoryUsage.toFixed(2)}MB`);
        } else {
            testResults.performance.failed++;
            testResults.performance.issues.push(`استخدام ذاكرة عالي: ${memoryUsage.toFixed(2)}MB`);
        }
    }
}

// 7. فحص الأخطاء في الكونسول
function checkConsoleErrors() {
    console.log('🐛 فحص الأخطاء في الكونسول...');
    
    // تسجيل الأخطاء
    const originalError = console.error;
    const errors = [];
    
    console.error = function(...args) {
        errors.push(args.join(' '));
        originalError.apply(console, args);
    };
    
    setTimeout(() => {
        if (errors.length === 0) {
            testResults.ui.passed++;
            console.log('✅ لا توجد أخطاء في الكونسول');
        } else {
            testResults.ui.failed++;
            testResults.ui.issues.push(`أخطاء في الكونسول: ${errors.length}`);
            errors.forEach(error => {
                testResults.ui.issues.push(`خطأ: ${error}`);
            });
        }
        
        // استعادة console.error الأصلي
        console.error = originalError;
    }, 2000);
}

// تشغيل جميع الاختبارات
async function runAllTests() {
    console.log('🚀 بدء تشغيل جميع الاختبارات...');
    
    await testAPIConnection();
    testUIElements();
    testSearchFunctionality();
    testNavigation();
    testCartFunctionality();
    testPerformance();
    checkConsoleErrors();
    
    // عرض النتائج النهائية
    setTimeout(() => {
        displayTestResults();
    }, 3000);
}

// عرض نتائج الاختبار
function displayTestResults() {
    console.log('\n📊 نتائج الفحص الشامل:');
    console.log('========================');
    
    Object.keys(testResults).forEach(category => {
        const result = testResults[category];
        const total = result.passed + result.failed;
        const percentage = total > 0 ? ((result.passed / total) * 100).toFixed(1) : 0;
        
        console.log(`\n${getCategoryName(category)}:`);
        console.log(`✅ نجح: ${result.passed}`);
        console.log(`❌ فشل: ${result.failed}`);
        console.log(`📈 النسبة: ${percentage}%`);
        
        if (result.issues.length > 0) {
            console.log('🔍 المشاكل المكتشفة:');
            result.issues.forEach(issue => {
                console.log(`  - ${issue}`);
            });
        }
    });
    
    // حساب النتيجة الإجمالية
    const totalPassed = Object.values(testResults).reduce((sum, cat) => sum + cat.passed, 0);
    const totalFailed = Object.values(testResults).reduce((sum, cat) => sum + cat.failed, 0);
    const overallPercentage = ((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1);
    
    console.log('\n🎯 النتيجة الإجمالية:');
    console.log(`✅ إجمالي النجاح: ${totalPassed}`);
    console.log(`❌ إجمالي الفشل: ${totalFailed}`);
    console.log(`📊 النسبة الإجمالية: ${overallPercentage}%`);
    
    if (overallPercentage >= 80) {
        console.log('🎉 التطبيق في حالة ممتازة!');
    } else if (overallPercentage >= 60) {
        console.log('⚠️ التطبيق يحتاج لبعض التحسينات');
    } else {
        console.log('🚨 التطبيق يحتاج لإصلاحات عاجلة');
    }
}

function getCategoryName(category) {
    const names = {
        api: '📡 الاتصال بـ API',
        ui: '🎨 واجهة المستخدم',
        search: '🔍 البحث',
        navigation: '🧭 التنقل',
        cart: '🛒 سلة التسوق',
        performance: '⚡ الأداء'
    };
    return names[category] || category;
}

// تشغيل الاختبارات عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
} else {
    runAllTests();
}

// تصدير النتائج للاستخدام الخارجي
window.testResults = testResults;
window.runAllTests = runAllTests;
