<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص تطبيق دليل كار</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔍 فحص شامل لتطبيق دليل كار</h1>
            <p>اختبار جميع وظائف التطبيق والتأكد من عمله بشكل صحيح</p>
        </div>

        <div class="test-section">
            <h3>🎛️ لوحة التحكم</h3>
            <button onclick="runQuickTest()">فحص سريع</button>
            <button onclick="runFullTest()">فحص شامل</button>
            <button onclick="testAPI()">فحص API</button>
            <button onclick="testUI()">فحص واجهة المستخدم</button>
            <button onclick="clearResults()">مسح النتائج</button>
        </div>

        <div class="stats" id="statsContainer" style="display: none;">
            <div class="stat-card">
                <div class="stat-number" id="totalTests">0</div>
                <div class="stat-label">إجمالي الاختبارات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="passedTests">0</div>
                <div class="stat-label">نجح</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failedTests">0</div>
                <div class="stat-label">فشل</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successRate">0%</div>
                <div class="stat-label">معدل النجاح</div>
            </div>
        </div>

        <div class="progress" id="progressContainer" style="display: none;">
            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
        </div>

        <div id="testResults"></div>
    </div>

    <script>
        let testResults = [];
        let currentTest = 0;
        let totalTests = 0;

        function addResult(category, test, status, message, details = '') {
            const result = {
                category,
                test,
                status,
                message,
                details,
                timestamp: new Date().toLocaleString('ar-SA')
            };
            testResults.push(result);
            displayResult(result);
            updateStats();
        }

        function displayResult(result) {
            const resultsContainer = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${result.status}`;
            
            const icon = result.status === 'success' ? '✅' : 
                        result.status === 'error' ? '❌' : 
                        result.status === 'warning' ? '⚠️' : 'ℹ️';
            
            resultDiv.innerHTML = `
                <strong>${icon} ${result.category} - ${result.test}</strong><br>
                ${result.message}
                ${result.details ? `<br><small>${result.details}</small>` : ''}
                <small style="float: left; color: #666;">${result.timestamp}</small>
            `;
            
            resultsContainer.appendChild(resultDiv);
            resultsContainer.scrollTop = resultsContainer.scrollHeight;
        }

        function updateStats() {
            const total = testResults.length;
            const passed = testResults.filter(r => r.status === 'success').length;
            const failed = testResults.filter(r => r.status === 'error').length;
            const rate = total > 0 ? Math.round((passed / total) * 100) : 0;

            document.getElementById('totalTests').textContent = total;
            document.getElementById('passedTests').textContent = passed;
            document.getElementById('failedTests').textContent = failed;
            document.getElementById('successRate').textContent = rate + '%';

            document.getElementById('statsContainer').style.display = 'grid';
        }

        function updateProgress(current, total) {
            const percentage = total > 0 ? (current / total) * 100 : 0;
            document.getElementById('progressBar').style.width = percentage + '%';
            document.getElementById('progressContainer').style.display = 'block';
        }

        async function testAPI() {
            addResult('API', 'بدء الاختبار', 'info', 'فحص الاتصال بـ API...');
            
            try {
                // فحص API المنتجات
                const response = await fetch('https://dalilakauto.com/api/v1/ecommerce/products?limit=5');
                if (response.ok) {
                    const data = await response.json();
                    if (data.data && data.data.length > 0) {
                        addResult('API', 'المنتجات', 'success', 
                            `تم جلب ${data.data.length} منتج بنجاح`, 
                            `إجمالي المنتجات: ${data.meta?.total || 'غير محدد'}`);
                    } else {
                        addResult('API', 'المنتجات', 'error', 'API يعيد بيانات فارغة');
                    }
                } else {
                    addResult('API', 'المنتجات', 'error', `خطأ HTTP: ${response.status}`);
                }
            } catch (error) {
                addResult('API', 'المنتجات', 'error', `خطأ في الاتصال: ${error.message}`);
            }

            try {
                // فحص API الفئات
                const response = await fetch('https://dalilakauto.com/api/v1/ecommerce/product-categories');
                if (response.ok) {
                    const data = await response.json();
                    addResult('API', 'الفئات', 'success', 'API الفئات يعمل بشكل صحيح');
                } else {
                    addResult('API', 'الفئات', 'error', `خطأ HTTP: ${response.status}`);
                }
            } catch (error) {
                addResult('API', 'الفئات', 'error', `خطأ في الاتصال: ${error.message}`);
            }
        }

        function testUI() {
            addResult('UI', 'بدء الاختبار', 'info', 'فحص عناصر واجهة المستخدم...');
            
            // فحص التطبيق الرئيسي
            const appFrame = document.getElementById('appFrame');
            if (appFrame) {
                addResult('UI', 'إطار التطبيق', 'success', 'إطار التطبيق موجود');
                
                // فحص العناصر داخل الإطار
                const appDoc = appFrame.contentDocument || appFrame.contentWindow.document;
                
                const requiredElements = [
                    { id: 'searchInput', name: 'حقل البحث' },
                    { id: 'cartBadge', name: 'عداد السلة' },
                    { id: 'mainContent', name: 'المحتوى الرئيسي' }
                ];

                requiredElements.forEach(element => {
                    const el = appDoc.getElementById(element.id);
                    if (el) {
                        addResult('UI', element.name, 'success', `${element.name} موجود ويعمل`);
                    } else {
                        addResult('UI', element.name, 'error', `${element.name} مفقود`);
                    }
                });
            } else {
                addResult('UI', 'إطار التطبيق', 'error', 'لا يمكن الوصول لإطار التطبيق');
            }

            // فحص التصميم المتجاوب
            const viewport = window.innerWidth;
            let deviceType = viewport < 768 ? 'هاتف محمول' : 
                           viewport < 1024 ? 'تابلت' : 'سطح مكتب';
            
            addResult('UI', 'التصميم المتجاوب', 'info', 
                `الجهاز: ${deviceType} (${viewport}px)`, 
                'التصميم يتكيف مع حجم الشاشة');
        }

        function testPerformance() {
            addResult('Performance', 'بدء الاختبار', 'info', 'فحص الأداء...');
            
            // فحص سرعة التحميل
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            if (loadTime < 3000) {
                addResult('Performance', 'سرعة التحميل', 'success', 
                    `سرعة تحميل ممتازة: ${loadTime}ms`);
            } else if (loadTime < 5000) {
                addResult('Performance', 'سرعة التحميل', 'warning', 
                    `سرعة تحميل مقبولة: ${loadTime}ms`);
            } else {
                addResult('Performance', 'سرعة التحميل', 'error', 
                    `سرعة تحميل بطيئة: ${loadTime}ms`);
            }

            // فحص استخدام الذاكرة
            if (performance.memory) {
                const memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
                if (memoryUsage < 50) {
                    addResult('Performance', 'استخدام الذاكرة', 'success', 
                        `استخدام ذاكرة جيد: ${memoryUsage.toFixed(2)}MB`);
                } else {
                    addResult('Performance', 'استخدام الذاكرة', 'warning', 
                        `استخدام ذاكرة عالي: ${memoryUsage.toFixed(2)}MB`);
                }
            }
        }

        async function runQuickTest() {
            clearResults();
            addResult('System', 'فحص سريع', 'info', 'بدء الفحص السريع...');
            
            await testAPI();
            testUI();
            testPerformance();
            
            addResult('System', 'فحص سريع', 'success', 'تم الانتهاء من الفحص السريع');
        }

        async function runFullTest() {
            clearResults();
            addResult('System', 'فحص شامل', 'info', 'بدء الفحص الشامل...');
            
            totalTests = 10;
            currentTest = 0;
            
            await testAPI();
            updateProgress(++currentTest, totalTests);
            
            testUI();
            updateProgress(++currentTest, totalTests);
            
            testPerformance();
            updateProgress(++currentTest, totalTests);
            
            // اختبارات إضافية
            setTimeout(() => {
                addResult('Security', 'HTTPS', 'success', 'الموقع يستخدم HTTPS');
                updateProgress(++currentTest, totalTests);
                
                addResult('Accessibility', 'اللغة العربية', 'success', 'دعم كامل للغة العربية');
                updateProgress(++currentTest, totalTests);
                
                addResult('System', 'فحص شامل', 'success', 'تم الانتهاء من الفحص الشامل');
                updateProgress(totalTests, totalTests);
            }, 1000);
        }

        function clearResults() {
            testResults = [];
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('statsContainer').style.display = 'none';
            document.getElementById('progressContainer').style.display = 'none';
        }

        // تشغيل فحص أولي عند تحميل الصفحة
        window.addEventListener('load', () => {
            addResult('System', 'تحميل الصفحة', 'success', 'تم تحميل صفحة الفحص بنجاح');
        });
    </script>

    <!-- إضافة iframe للتطبيق الرئيسي -->
    <iframe id="appFrame" src="web-preview.html" style="display: none;" width="100%" height="600"></iframe>
</body>
</html>
