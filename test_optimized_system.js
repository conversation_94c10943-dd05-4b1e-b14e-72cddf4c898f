/**
 * اختبار شامل للنظام المحسن لإدارة المنتجات
 * Comprehensive Test for Optimized Product Management System
 */

console.log('🧪 بدء اختبار النظام المحسن لإدارة المنتجات...');

// اختبار 1: فحص وجود النظام المحسن
function testOptimizedSystemExists() {
    console.log('\n📋 اختبار 1: فحص وجود النظام المحسن');
    
    if (typeof optimizedProductManager !== 'undefined') {
        console.log('✅ النظام المحسن موجود');
        console.log('📊 نوع الكائن:', typeof optimizedProductManager);
        console.log('🔧 الطرق المتاحة:', Object.getOwnPropertyNames(Object.getPrototypeOf(optimizedProductManager)));
        return true;
    } else {
        console.log('❌ النظام المحسن غير موجود');
        return false;
    }
}

// اختبار 2: فحص التهيئة
async function testInitialization() {
    console.log('\n📋 اختبار 2: فحص التهيئة');
    
    try {
        if (!optimizedProductManager.isInitialized) {
            console.log('🚀 تهيئة النظام...');
            await optimizedProductManager.init();
        }
        
        if (optimizedProductManager.isInitialized) {
            console.log('✅ تم تهيئة النظام بنجاح');
            return true;
        } else {
            console.log('❌ فشل في تهيئة النظام');
            return false;
        }
    } catch (error) {
        console.log('❌ خطأ في التهيئة:', error);
        return false;
    }
}

// اختبار 3: فحص تحميل المنتجات
function testProductLoading() {
    console.log('\n📋 اختبار 3: فحص تحميل المنتجات');
    
    const stats = optimizedProductManager.getPerformanceStats();
    
    console.log('📊 إحصائيات التحميل:');
    console.log(`   - المنتجات المحملة: ${stats.loadedProducts}`);
    console.log(`   - إجمالي المنتجات: ${stats.totalProducts}`);
    console.log(`   - الصفحة الحالية: ${stats.currentPage}`);
    console.log(`   - استخدام الذاكرة: ${stats.memoryUsage.toFixed(2)} MB`);
    
    if (stats.loadedProducts > 0) {
        console.log('✅ تم تحميل المنتجات بنجاح');
        return true;
    } else {
        console.log('❌ لم يتم تحميل أي منتجات');
        return false;
    }
}

// اختبار 4: فحص نظام الكاش
function testCacheSystem() {
    console.log('\n📋 اختبار 4: فحص نظام الكاش');
    
    const stats = optimizedProductManager.getPerformanceStats();
    
    console.log('💾 إحصائيات الكاش:');
    console.log(`   - حجم الكاش: ${stats.cacheSize} عنصر`);
    console.log(`   - كاش البحث: ${stats.searchCacheSize} استعلام`);
    
    if (stats.cacheSize > 0) {
        console.log('✅ نظام الكاش يعمل');
        return true;
    } else {
        console.log('⚠️ الكاش فارغ (قد يكون طبيعياً في البداية)');
        return true; // ليس خطأ بالضرورة
    }
}

// اختبار 5: فحص فهرس البحث
function testSearchIndex() {
    console.log('\n📋 اختبار 5: فحص فهرس البحث');
    
    const stats = optimizedProductManager.getPerformanceStats();
    
    console.log('🔍 إحصائيات فهرس البحث:');
    console.log(`   - عدد الكلمات المفتاحية: ${stats.searchIndexSize}`);
    
    if (stats.searchIndexSize > 0) {
        console.log('✅ فهرس البحث تم بناؤه');
        return true;
    } else {
        console.log('❌ فهرس البحث فارغ');
        return false;
    }
}

// اختبار 6: فحص البحث المحلي
function testLocalSearch() {
    console.log('\n📋 اختبار 6: فحص البحث المحلي');
    
    try {
        // اختبار البحث بكلمات مختلفة
        const testQueries = ['بوشة', 'رباط', 'فرامل', 'محرك'];
        
        testQueries.forEach(query => {
            const results = optimizedProductManager.searchLocally(query);
            console.log(`🔍 البحث عن "${query}": ${results.length} نتيجة`);
            
            if (results.length > 0) {
                console.log(`   - أول نتيجة: ${results[0].name}`);
            }
        });
        
        console.log('✅ البحث المحلي يعمل');
        return true;
    } catch (error) {
        console.log('❌ خطأ في البحث المحلي:', error);
        return false;
    }
}

// اختبار 7: فحص استخراج الكلمات المفتاحية
function testKeywordExtraction() {
    console.log('\n📋 اختبار 7: فحص استخراج الكلمات المفتاحية');
    
    try {
        const testProduct = {
            name: 'بوشة محرك تويوتا كورولا',
            description: 'قطعة غيار أصلية للمحرك',
            sku: 'TOY-ENG-001',
            brand: { name: 'تويوتا' }
        };
        
        const keywords = optimizedProductManager.extractKeywords(testProduct);
        console.log('🔤 الكلمات المستخرجة:', keywords);
        
        if (keywords.length > 0) {
            console.log('✅ استخراج الكلمات المفتاحية يعمل');
            return true;
        } else {
            console.log('❌ لم يتم استخراج أي كلمات');
            return false;
        }
    } catch (error) {
        console.log('❌ خطأ في استخراج الكلمات:', error);
        return false;
    }
}

// اختبار 8: فحص الأداء
function testPerformance() {
    console.log('\n📋 اختبار 8: فحص الأداء');
    
    const startTime = performance.now();
    
    // اختبار سرعة البحث
    const searchResults = optimizedProductManager.searchLocally('محرك');
    
    const endTime = performance.now();
    const searchTime = endTime - startTime;
    
    console.log('⚡ نتائج الأداء:');
    console.log(`   - وقت البحث: ${searchTime.toFixed(2)} ms`);
    console.log(`   - عدد النتائج: ${searchResults.length}`);
    
    if (searchTime < 100) { // أقل من 100ms
        console.log('✅ الأداء ممتاز');
        return true;
    } else if (searchTime < 500) {
        console.log('⚠️ الأداء مقبول');
        return true;
    } else {
        console.log('❌ الأداء بطيء');
        return false;
    }
}

// اختبار 9: فحص التكامل مع النظام القديم
function testLegacyIntegration() {
    console.log('\n📋 اختبار 9: فحص التكامل مع النظام القديم');
    
    try {
        // فحص وجود المتغيرات القديمة
        const hasProducts = typeof products !== 'undefined' && Array.isArray(products);
        const hasAllProductsData = typeof allProductsData !== 'undefined' && Array.isArray(allProductsData);
        
        console.log('🔗 حالة التكامل:');
        console.log(`   - متغير products: ${hasProducts ? '✅ موجود' : '❌ مفقود'}`);
        console.log(`   - متغير allProductsData: ${hasAllProductsData ? '✅ موجود' : '❌ مفقود'}`);
        
        if (hasProducts) {
            console.log(`   - عدد المنتجات في products: ${products.length}`);
        }
        
        if (hasAllProductsData) {
            console.log(`   - عدد المنتجات في allProductsData: ${allProductsData.length}`);
        }
        
        return hasProducts || hasAllProductsData;
    } catch (error) {
        console.log('❌ خطأ في فحص التكامل:', error);
        return false;
    }
}

// اختبار 10: فحص الدوال المحسنة
function testOptimizedFunctions() {
    console.log('\n📋 اختبار 10: فحص الدوال المحسنة');
    
    const functions = [
        'loadAllProductsFromAllPages',
        'performSearch'
    ];
    
    let allFunctionsExist = true;
    
    functions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ ${funcName} موجودة ومحدثة`);
        } else {
            console.log(`❌ ${funcName} مفقودة`);
            allFunctionsExist = false;
        }
    });
    
    return allFunctionsExist;
}

// تشغيل جميع الاختبارات
async function runAllTests() {
    console.log('🧪 بدء تشغيل جميع الاختبارات...\n');
    
    const tests = [
        { name: 'وجود النظام', func: testOptimizedSystemExists },
        { name: 'التهيئة', func: testInitialization },
        { name: 'تحميل المنتجات', func: testProductLoading },
        { name: 'نظام الكاش', func: testCacheSystem },
        { name: 'فهرس البحث', func: testSearchIndex },
        { name: 'البحث المحلي', func: testLocalSearch },
        { name: 'استخراج الكلمات', func: testKeywordExtraction },
        { name: 'الأداء', func: testPerformance },
        { name: 'التكامل', func: testLegacyIntegration },
        { name: 'الدوال المحسنة', func: testOptimizedFunctions }
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    for (const test of tests) {
        try {
            const result = await test.func();
            if (result) {
                passedTests++;
            }
        } catch (error) {
            console.log(`❌ خطأ في اختبار ${test.name}:`, error);
        }
    }
    
    // النتيجة النهائية
    console.log('\n🎯 النتيجة النهائية:');
    console.log(`✅ اختبارات ناجحة: ${passedTests}/${totalTests}`);
    console.log(`📊 معدل النجاح: ${((passedTests/totalTests) * 100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
        console.log('🎉 جميع الاختبارات نجحت! النظام المحسن يعمل بشكل مثالي');
    } else if (passedTests >= totalTests * 0.8) {
        console.log('✅ النظام المحسن يعمل بشكل جيد مع بعض التحسينات المطلوبة');
    } else {
        console.log('⚠️ النظام المحسن يحتاج إلى مراجعة وإصلاحات');
    }
    
    // عرض إحصائيات مفصلة
    if (typeof optimizedProductManager !== 'undefined' && optimizedProductManager.isInitialized) {
        console.log('\n📊 إحصائيات مفصلة للنظام المحسن:');
        const stats = optimizedProductManager.getPerformanceStats();
        console.table(stats);
    }
}

// تشغيل الاختبارات عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(runAllTests, 3000); // انتظار تهيئة النظام
    });
} else {
    setTimeout(runAllTests, 1000);
}

// تصدير الدوال للاستخدام اليدوي
window.testOptimizedSystem = {
    runAllTests,
    testOptimizedSystemExists,
    testInitialization,
    testProductLoading,
    testCacheSystem,
    testSearchIndex,
    testLocalSearch,
    testKeywordExtraction,
    testPerformance,
    testLegacyIntegration,
    testOptimizedFunctions
};

console.log('🔧 يمكنك تشغيل الاختبارات يدوياً باستخدام: testOptimizedSystem.runAllTests()');
