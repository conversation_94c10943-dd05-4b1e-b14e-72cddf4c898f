# 📱 دليل كار - تطبيق قطع غيار السيارات React Native

تطبيق محمول متطور لبيع قطع غيار السيارات مبني بـ React Native ومتصل بـ APIs شاملة.

## ✨ المميزات

### 🎯 الشاشات الرئيسية
- **شاشة البداية** (Splash Screen)
- **شاشات التعريف** (Onboarding)
- **تسجيل الدخول والتسجيل**
- **الصفحة الرئيسية** (Dashboard)
- **البحث المتقدم**
- **عرض المنتجات**
- **سلة التسوق**
- **قائمة الأمنيات**
- **الملف الشخصي**
- **الطلبات**
- **الإعدادات**

### 🛒 مميزات التسوق
- **بحث متقدم** بالماركة والموديل والسنة
- **فلاتر ذكية** للمنتجات
- **إضافة للسلة** والمفضلة
- **نظام طلبات** متكامل
- **تتبع الطلبات**
- **مراجعات وتقييمات**

### 🎨 التصميم والواجهة
- **تصميم عربي** أصيل
- **واجهة سهلة الاستخدام**
- **ألوان متناسقة**
- **أيقونات احترافية**
- **تجربة مستخدم سلسة**

## 🛠 المتطلبات

### البيئة التطويرية
- **Node.js** `>=16.0.0`
- **npm** `>=8.0.0`
- **React Native CLI**
- **Android Studio** (للتطوير على Android)
- **Xcode** (للتطوير على iOS - macOS فقط)

### أنظمة التشغيل المدعومة
- **Android** `>=5.0` (API Level 21+)
- **iOS** `>=11.0`

## 🚀 التقنيات المستخدمة

### الأساسية
- **React Native** `0.72.6`
- **React** `18.2.0`
- **TypeScript** `4.8.4`

### التنقل والواجهة
- **React Navigation** `6.x`
- **React Native Vector Icons**
- **FontAwesome Icons**
- **React Native Reanimated**
- **React Native Gesture Handler**

### إدارة الحالة
- **MobX** `6.10.2`
- **MobX React** `9.0.1`

### الشبكة والبيانات
- **Axios** `1.6.0`
- **APIs متكاملة** مع الخادم

## 📦 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd dMobileApp
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. تثبيت pods (iOS فقط)
```bash
cd ios && pod install && cd ..
```

### 4. تشغيل التطبيق

#### Android
```bash
npm run android
```

#### iOS
```bash
npm run ios
```

### 5. تشغيل Metro Server
```bash
npm start
```

## 🏗 بناء التطبيق للإنتاج

### Android APK
```bash
npm run build:android
```

### iOS Archive
```bash
npm run build:ios
```

## 📁 هيكل المشروع

```
dMobileApp/
├── src/
│   ├── components/          # المكونات المشتركة
│   ├── screens/            # شاشات التطبيق
│   ├── navigation/         # إعدادات التنقل
│   ├── store/             # إدارة الحالة (MobX)
│   ├── services/          # خدمات API
│   ├── styles/            # الأنماط والثيمات
│   ├── constants/         # الثوابت
│   ├── contexts/          # React Contexts
│   └── data/              # البيانات الثابتة
├── assets/                # الصور والملفات
├── android/               # مشروع Android
├── ios/                   # مشروع iOS
├── App.js                 # نقطة البداية
├── index.js               # ملف التسجيل
└── package.json           # تبعيات المشروع
```

## 🔧 الإعدادات

### متغيرات البيئة
قم بإنشاء ملف `.env` في جذر المشروع:
```
API_BASE_URL=https://dalilakauto.com/api/v1
APP_NAME=دليل كار
```

### إعدادات API
تحديث ملف `src/constants/apiEndpoints.js` بعناوين API الصحيحة.

## 🧪 الاختبار

```bash
npm test
```

## 📱 معلومات التطبيق

- **اسم التطبيق:** دليل كار - قطع غيار السيارات
- **Package Name:** `com.dalilakauto.app`
- **الإصدار:** `1.0.0`
- **المطور:** Dalila Car Auto Parts

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للـ branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم والتواصل

- **الموقع الإلكتروني:** [dalilakauto.com](https://dalilakauto.com)
- **البريد الإلكتروني:** <EMAIL>

---

**🚗 دليل كار - دليلك الشامل لقطع غيار السيارات**
