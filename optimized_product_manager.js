/**
 * نظام إدارة المنتجات المحسن لتطبيق دليل كار
 * Optimized Product Management System for Dalila Car App
 */

class OptimizedProductManager {
    constructor() {
        this.products = [];
        this.cache = new Map();
        this.searchIndex = new Map();
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalProducts = 0;
        this.loading = false;
        this.searchCache = new Map();
        
        this.init();
    }

    async init() {
        console.log('🚀 تهيئة نظام إدارة المنتجات المحسن...');
        
        // تحميل الصفحة الأولى
        await this.loadProducts(1);
        
        // إعداد البحث المحسن
        this.setupOptimizedSearch();
        
        // إعداد التمرير اللانهائي
        this.setupInfiniteScroll();
        
        // بدء التحديث في الخلفية
        this.startBackgroundSync();
    }

    // 1. تحميل المنتجات بالصفحات
    async loadProducts(page = 1, append = false) {
        if (this.loading) return;
        
        this.loading = true;
        this.showLoadingState();
        
        try {
            console.log(`📦 تحميل الصفحة ${page}...`);
            
            // فحص الكاش أولاً
            const cacheKey = `products_page_${page}`;
            let data = this.cache.get(cacheKey);
            
            if (!data) {
                const response = await fetch(`https://dalilakauto.com/api/v1/ecommerce/products?page=${page}&limit=${this.pageSize}`);
                data = await response.json();
                
                // حفظ في الكاش
                this.cache.set(cacheKey, data);
                
                // تنظيف الكاش إذا كان كبيراً
                if (this.cache.size > 50) {
                    const firstKey = this.cache.keys().next().value;
                    this.cache.delete(firstKey);
                }
            }
            
            if (data.error === false && data.data) {
                if (append) {
                    this.products = [...this.products, ...data.data];
                } else {
                    this.products = data.data;
                }
                
                this.totalProducts = data.meta?.total || data.data.length;
                this.currentPage = page;
                
                // بناء فهرس البحث
                this.buildSearchIndex(data.data);
                
                // عرض المنتجات
                this.displayProducts(this.products);
                
                console.log(`✅ تم تحميل ${data.data.length} منتج (إجمالي: ${this.products.length})`);
            }
            
        } catch (error) {
            console.error('❌ خطأ في تحميل المنتجات:', error);
            this.showErrorState();
        } finally {
            this.loading = false;
            this.hideLoadingState();
        }
    }

    // 2. بناء فهرس البحث
    buildSearchIndex(products) {
        products.forEach(product => {
            const keywords = this.extractKeywords(product);
            keywords.forEach(keyword => {
                if (!this.searchIndex.has(keyword)) {
                    this.searchIndex.set(keyword, []);
                }
                this.searchIndex.get(keyword).push(product.id);
            });
        });
    }

    extractKeywords(product) {
        const text = [
            product.name,
            product.description,
            product.content,
            product.sku,
            product.brand?.name,
            product.category?.name
        ].filter(Boolean).join(' ').toLowerCase();
        
        // تنظيف النص وتقسيمه
        return text
            .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 1);
    }

    // 3. البحث المحسن
    setupOptimizedSearch() {
        const searchInput = document.getElementById('searchInput');
        if (!searchInput) return;

        // إزالة المستمعات القديمة
        searchInput.removeEventListener('input', this.oldSearchHandler);
        
        // إضافة البحث المحسن مع debounce
        this.oldSearchHandler = this.debounce(async (e) => {
            const query = e.target.value.trim();
            await this.performOptimizedSearch(query);
        }, 300);
        
        searchInput.addEventListener('input', this.oldSearchHandler);
        console.log('🔍 تم إعداد البحث المحسن');
    }

    async performOptimizedSearch(query) {
        if (query.length < 2) {
            this.displayProducts(this.products);
            return;
        }

        // فحص كاش البحث
        if (this.searchCache.has(query)) {
            const cachedResults = this.searchCache.get(query);
            this.displaySearchResults(cachedResults, query);
            return;
        }

        this.showSearchLoading();

        try {
            // البحث المحلي أولاً (سريع)
            const localResults = this.searchLocally(query);
            
            if (localResults.length > 0) {
                this.displaySearchResults(localResults, query);
                this.searchCache.set(query, localResults);
            } else {
                // البحث في الخادم إذا لم توجد نتائج محلية
                const serverResults = await this.searchOnServer(query);
                this.displaySearchResults(serverResults, query);
                this.searchCache.set(query, serverResults);
            }

        } catch (error) {
            console.error('❌ خطأ في البحث:', error);
            this.showSearchError();
        } finally {
            this.hideSearchLoading();
        }
    }

    searchLocally(query) {
        const keywords = query.toLowerCase().split(/\s+/);
        const matchingIds = new Set();

        keywords.forEach(keyword => {
            // البحث الدقيق
            if (this.searchIndex.has(keyword)) {
                this.searchIndex.get(keyword).forEach(id => matchingIds.add(id));
            }
            
            // البحث الضبابي
            this.searchIndex.forEach((ids, indexedKeyword) => {
                if (indexedKeyword.includes(keyword) || keyword.includes(indexedKeyword)) {
                    ids.forEach(id => matchingIds.add(id));
                }
            });
        });

        return this.products.filter(product => matchingIds.has(product.id));
    }

    async searchOnServer(query) {
        const response = await fetch(`https://dalilakauto.com/api/v1/ecommerce/products/search?q=${encodeURIComponent(query)}&limit=50`);
        const data = await response.json();
        return data.error === false ? data.data : [];
    }

    // 4. التمرير اللانهائي
    setupInfiniteScroll() {
        let isScrolling = false;
        
        window.addEventListener('scroll', this.debounce(() => {
            if (isScrolling || this.loading) return;
            
            const scrollPosition = window.innerHeight + window.scrollY;
            const documentHeight = document.documentElement.offsetHeight;
            
            // تحميل المزيد عند الوصول لـ 80% من الصفحة
            if (scrollPosition >= documentHeight * 0.8) {
                isScrolling = true;
                this.loadMoreProducts().finally(() => {
                    isScrolling = false;
                });
            }
        }, 100));
        
        console.log('📜 تم إعداد التمرير اللانهائي');
    }

    async loadMoreProducts() {
        const nextPage = this.currentPage + 1;
        const maxPages = Math.ceil(this.totalProducts / this.pageSize);
        
        if (nextPage <= maxPages) {
            await this.loadProducts(nextPage, true);
        }
    }

    // 5. التحديث في الخلفية
    startBackgroundSync() {
        // تحديث كل 5 دقائق
        setInterval(async () => {
            try {
                const lastUpdate = localStorage.getItem('lastProductUpdate');
                const now = Date.now();
                
                // تحديث إذا مر أكثر من 5 دقائق
                if (!lastUpdate || (now - parseInt(lastUpdate)) > 5 * 60 * 1000) {
                    await this.syncProducts();
                    localStorage.setItem('lastProductUpdate', now.toString());
                }
            } catch (error) {
                console.log('🔄 فشل التحديث في الخلفية:', error);
            }
        }, 5 * 60 * 1000);
    }

    async syncProducts() {
        console.log('🔄 تحديث المنتجات في الخلفية...');
        
        // تحديث الصفحة الأولى فقط في الخلفية
        const response = await fetch(`https://dalilakauto.com/api/v1/ecommerce/products?page=1&limit=${this.pageSize}`);
        const data = await response.json();
        
        if (data.error === false && data.data) {
            // تحديث الكاش
            this.cache.set('products_page_1', data);
            
            // تحديث العدد الإجمالي
            if (data.meta?.total && data.meta.total !== this.totalProducts) {
                this.totalProducts = data.meta.total;
                console.log(`📊 تم تحديث العدد الإجمالي: ${this.totalProducts}`);
            }
        }
    }

    // 6. عرض المنتجات المحسن
    displayProducts(products) {
        const container = document.getElementById('productsGrid') || document.querySelector('.products-grid');
        if (!container) return;

        // عرض تدريجي للمنتجات (Virtual Scrolling مبسط)
        this.renderProductsInBatches(products, container);
    }

    renderProductsInBatches(products, container, batchSize = 10) {
        let currentIndex = 0;
        
        const renderBatch = () => {
            const batch = products.slice(currentIndex, currentIndex + batchSize);
            
            batch.forEach(product => {
                const productElement = this.createProductElement(product);
                container.appendChild(productElement);
            });
            
            currentIndex += batchSize;
            
            if (currentIndex < products.length) {
                // تأخير قصير للسماح للمتصفح بالتنفس
                setTimeout(renderBatch, 0);
            }
        };
        
        // مسح المحتوى السابق
        container.innerHTML = '';
        renderBatch();
    }

    createProductElement(product) {
        const div = document.createElement('div');
        div.className = 'product-card';
        div.innerHTML = `
            <div class="product-image">
                <img src="${product.image || '/placeholder.jpg'}" alt="${product.name}" loading="lazy">
            </div>
            <div class="product-info">
                <h3>${product.name}</h3>
                <p class="price">${product.price} ريال</p>
                <button onclick="addToCart(${product.id})" class="add-to-cart-btn">
                    إضافة للسلة
                </button>
            </div>
        `;
        return div;
    }

    // 7. حالات التحميل والأخطاء
    showLoadingState() {
        const loader = document.getElementById('productsLoader');
        if (loader) loader.style.display = 'block';
    }

    hideLoadingState() {
        const loader = document.getElementById('productsLoader');
        if (loader) loader.style.display = 'none';
    }

    showSearchLoading() {
        console.log('🔍 جاري البحث...');
    }

    hideSearchLoading() {
        console.log('✅ انتهى البحث');
    }

    showErrorState() {
        console.error('❌ خطأ في تحميل المنتجات');
    }

    showSearchError() {
        console.error('❌ خطأ في البحث');
    }

    displaySearchResults(results, query) {
        console.log(`🔍 نتائج البحث عن "${query}": ${results.length} منتج`);
        this.displayProducts(results);
    }

    // 8. أدوات مساعدة
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 9. إحصائيات الأداء
    getPerformanceStats() {
        return {
            totalProducts: this.totalProducts,
            loadedProducts: this.products.length,
            cacheSize: this.cache.size,
            searchIndexSize: this.searchIndex.size,
            searchCacheSize: this.searchCache.size,
            currentPage: this.currentPage,
            memoryUsage: this.products.length * 0.001 // تقدير تقريبي بالMB
        };
    }
}

// تهيئة النظام المحسن
const optimizedProductManager = new OptimizedProductManager();

// تصدير للاستخدام العام
window.optimizedProductManager = optimizedProductManager;

console.log('🎉 تم تهيئة نظام إدارة المنتجات المحسن بنجاح!');
console.log('📊 للحصول على إحصائيات الأداء: optimizedProductManager.getPerformanceStats()');
