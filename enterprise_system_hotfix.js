/**
 * Enterprise System Hotfix
 * إصلاح سريع للمشاكل المكتشفة في النظام المتقدم
 */

// إصلاح مشكلة التهيئة
if (typeof window !== 'undefined' && window.EnterpriseProductSystem) {
    const originalInit = window.EnterpriseProductSystem.prototype.init;
    
    window.EnterpriseProductSystem.prototype.init = async function() {
        console.log('🔧 تطبيق الإصلاح السريع...');
        
        try {
            // تهيئة الأنظمة الأساسية أولاً
            await this.initializeCache();
            await this.initializeSearchEngine();
            await this.initializePerformanceMonitoring();
            
            // تهيئة الأنظمة الاختيارية
            try {
                await this.initializeServiceWorker();
            } catch (error) {
                console.warn('Service Worker initialization failed:', error);
            }
            
            try {
                await this.initializeWebSocket();
            } catch (error) {
                console.warn('WebSocket initialization failed:', error);
            }
            
            try {
                this.initializeAnalytics();
            } catch (error) {
                console.warn('Analytics initialization failed:', error);
            }
            
            // تحميل البيانات الأولية
            await this.loadInitialData();
            
            // إعداد المستمعات
            this.setupEventListeners();
            this.startBackgroundTasks();
            
            console.log('✅ تم تطبيق الإصلاح السريع بنجاح');
            this.trackEvent('system_initialized', { timestamp: Date.now() });
            
        } catch (error) {
            console.error('❌ فشل في تطبيق الإصلاح:', error);
            throw error;
        }
    };
    
    // إصلاح مشكلة Analytics
    if (!window.EnterpriseProductSystem.prototype.initializeAnalytics) {
        window.EnterpriseProductSystem.prototype.initializeAnalytics = function() {
            if (!this.config.enableAnalytics) return;

            // تأكد من وجود الكائن
            if (!this.analytics) {
                this.analytics = {};
            }

            this.analytics = {
                sessionId: this.generateSessionId(),
                userId: this.getUserId(),
                events: [],
                batchSize: 10,
                flushInterval: 30000
            };

            setInterval(() => {
                this.flushAnalytics();
            }, this.analytics.flushInterval);

            console.log('📈 Analytics system initialized (hotfix)');
        };
    }
    
    // إصلاح مشكلة Service Worker
    if (!window.EnterpriseProductSystem.prototype.initializeServiceWorker) {
        window.EnterpriseProductSystem.prototype.initializeServiceWorker = async function() {
            if (!('serviceWorker' in navigator)) return null;
            
            try {
                const registration = await navigator.serviceWorker.register('/sw-enterprise.js');
                console.log('🔧 Service Worker registered (hotfix)');
                return registration;
            } catch (error) {
                console.warn('Service Worker registration failed:', error);
                return null;
            }
        };
    }
    
    // إصلاح مشكلة WebSocket
    if (!window.EnterpriseProductSystem.prototype.initializeWebSocket) {
        window.EnterpriseProductSystem.prototype.initializeWebSocket = async function() {
            if (!this.config.enableWebSocket) return;
            
            try {
                this.websocket = new WebSocket('wss://dalilakauto.com/ws');
                
                this.websocket.onopen = () => {
                    console.log('🔗 WebSocket connected (hotfix)');
                    this.trackEvent('websocket_connected');
                };
                
                this.websocket.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    this.handleRealtimeUpdate(data);
                };
                
                this.websocket.onclose = () => {
                    console.log('🔗 WebSocket disconnected, attempting reconnect...');
                    setTimeout(() => this.initializeWebSocket(), 5000);
                };
                
            } catch (error) {
                console.warn('WebSocket initialization failed:', error);
            }
        };
    }
    
    // إصلاح دوال المساعدة
    if (!window.EnterpriseProductSystem.prototype.generateSessionId) {
        window.EnterpriseProductSystem.prototype.generateSessionId = function() {
            return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        };
    }
    
    if (!window.EnterpriseProductSystem.prototype.getUserId) {
        window.EnterpriseProductSystem.prototype.getUserId = function() {
            let userId = localStorage.getItem('dalila_user_id');
            if (!userId) {
                userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                localStorage.setItem('dalila_user_id', userId);
            }
            return userId;
        };
    }
    
    if (!window.EnterpriseProductSystem.prototype.trackEvent) {
        window.EnterpriseProductSystem.prototype.trackEvent = function(eventName, properties = {}) {
            if (!this.config.enableAnalytics || !this.analytics) return;

            // تأكد من وجود مصفوفة الأحداث
            if (!this.analytics.events) {
                this.analytics.events = [];
            }

            const event = {
                name: eventName,
                properties: {
                    ...properties,
                    sessionId: this.analytics.sessionId,
                    userId: this.analytics.userId,
                    timestamp: Date.now(),
                    url: window.location.href
                }
            };

            this.analytics.events.push(event);

            if (this.analytics.events.length >= this.analytics.batchSize) {
                this.flushAnalytics();
            }
        };
    }
    
    if (!window.EnterpriseProductSystem.prototype.flushAnalytics) {
        window.EnterpriseProductSystem.prototype.flushAnalytics = async function() {
            if (!this.analytics || this.analytics.events.length === 0) return;
            
            const events = [...this.analytics.events];
            this.analytics.events = [];
            
            try {
                console.log('📊 Flushing analytics:', events.length, 'events');
            } catch (error) {
                console.warn('Failed to flush analytics:', error);
                this.analytics.events.unshift(...events);
            }
        };
    }
    
    if (!window.EnterpriseProductSystem.prototype.handleRealtimeUpdate) {
        window.EnterpriseProductSystem.prototype.handleRealtimeUpdate = function(data) {
            console.log('📡 Real-time update received:', data);
        };
    }
    
    if (!window.EnterpriseProductSystem.prototype.setupEventListeners) {
        window.EnterpriseProductSystem.prototype.setupEventListeners = function() {
            window.addEventListener('online', () => {
                this.trackEvent('connection_restored');
            });
            
            window.addEventListener('offline', () => {
                this.trackEvent('connection_lost');
            });
            
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.trackEvent('page_hidden');
                    this.flushAnalytics();
                } else {
                    this.trackEvent('page_visible');
                }
            });
        };
    }
    
    if (!window.EnterpriseProductSystem.prototype.startBackgroundTasks) {
        window.EnterpriseProductSystem.prototype.startBackgroundTasks = function() {
            setInterval(() => {
                this.cleanupCache();
            }, 300000); // Every 5 minutes
            
            setInterval(() => {
                this.syncWithServer();
            }, 600000); // Every 10 minutes
        };
    }
    
    if (!window.EnterpriseProductSystem.prototype.cleanupCache) {
        window.EnterpriseProductSystem.prototype.cleanupCache = function() {
            console.log('🧹 Cleaning up cache...');
        };
    }
    
    if (!window.EnterpriseProductSystem.prototype.syncWithServer) {
        window.EnterpriseProductSystem.prototype.syncWithServer = function() {
            console.log('🔄 Syncing with server...');
        };
    }
    
    if (!window.EnterpriseProductSystem.prototype.loadInitialData) {
        window.EnterpriseProductSystem.prototype.loadInitialData = async function() {
            await this.loadProducts(1);
        };
    }
    
    console.log('🔧 Enterprise System Hotfix applied successfully');
}

// إصلاح مشكلة Performance Monitor
if (typeof window !== 'undefined' && window.EnterprisePerformanceMonitor) {
    if (!window.EnterprisePerformanceMonitor.prototype.getMetrics) {
        window.EnterprisePerformanceMonitor.prototype.getMetrics = function() {
            return {
                webVitals: Object.fromEntries(this.metrics.webVitals || new Map()),
                customMetrics: {},
                networkSummary: null,
                memorySummary: null,
                errorSummary: null
            };
        };
    }
}

// تطبيق الإصلاح تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        if (window.enterpriseSystem && typeof window.enterpriseSystem.init === 'function') {
            console.log('🔧 تطبيق الإصلاح على النظام الموجود...');
            
            // إعادة تهيئة النظام مع الإصلاح
            window.enterpriseSystem.init().then(() => {
                console.log('✅ تم تطبيق الإصلاح بنجاح');
            }).catch(error => {
                console.error('❌ فشل في تطبيق الإصلاح:', error);
            });
        }
    }, 1000);
});

console.log('🔧 Enterprise System Hotfix loaded');
