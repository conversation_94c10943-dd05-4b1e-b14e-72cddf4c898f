/**
 * Enterprise Performance Monitor
 * نظام مراقبة الأداء المتقدم - مستوى التطبيقات الكبيرة
 * 
 * Features:
 * - Real-time performance monitoring
 * - Core Web Vitals tracking
 * - Memory leak detection
 * - Network performance analysis
 * - User experience metrics
 * - Automated alerts and reporting
 */

class EnterprisePerformanceMonitor {
    constructor(config = {}) {
        this.config = {
            reportingEndpoint: config.reportingEndpoint || '/api/performance',
            alertThresholds: {
                LCP: 2500,      // Largest Contentful Paint
                FID: 100,       // First Input Delay
                CLS: 0.1,       // Cumulative Layout Shift
                TTFB: 600,      // Time to First Byte
                memoryUsage: 90, // Memory usage percentage (increased threshold)
                ...config.alertThresholds
            },
            sampleRate: config.sampleRate || 0.1, // 10% sampling
            batchSize: config.batchSize || 20,
            flushInterval: config.flushInterval || 30000, // 30 seconds
            enableRealTimeAlerts: config.enableRealTimeAlerts !== false,
            ...config
        };

        this.metrics = {
            webVitals: new Map(),
            customMetrics: new Map(),
            networkMetrics: [],
            memoryMetrics: [],
            userInteractions: [],
            errors: []
        };

        this.observers = [];
        this.isMonitoring = false;
        this.sessionId = this.generateSessionId();
        this.startTime = performance.now();

        this.init();
    }

    async init() {
        console.log('📊 Initializing Enterprise Performance Monitor...');

        try {
            // Initialize Core Web Vitals monitoring
            this.initWebVitalsMonitoring();
            
            // Initialize custom metrics
            this.initCustomMetrics();
            
            // Initialize network monitoring
            this.initNetworkMonitoring();
            
            // Initialize memory monitoring
            this.initMemoryMonitoring();
            
            // Initialize user interaction tracking
            this.initUserInteractionTracking();
            
            // Initialize error tracking
            this.initErrorTracking();
            
            // Start automated reporting
            this.startAutomatedReporting();
            
            this.isMonitoring = true;
            console.log('✅ Enterprise Performance Monitor initialized');
            
        } catch (error) {
            console.error('❌ Failed to initialize Performance Monitor:', error);
        }
    }

    /**
     * Core Web Vitals Monitoring
     */
    initWebVitalsMonitoring() {
        // Largest Contentful Paint (LCP)
        this.observeWebVital('LCP', 'largest-contentful-paint', (entry) => {
            const lcp = entry.startTime;
            this.recordMetric('LCP', lcp);
            
            if (lcp > this.config.alertThresholds.LCP) {
                this.triggerAlert('LCP_THRESHOLD_EXCEEDED', { value: lcp, threshold: this.config.alertThresholds.LCP });
            }
        });

        // First Input Delay (FID)
        this.observeWebVital('FID', 'first-input', (entry) => {
            const fid = entry.processingStart - entry.startTime;
            this.recordMetric('FID', fid);
            
            if (fid > this.config.alertThresholds.FID) {
                this.triggerAlert('FID_THRESHOLD_EXCEEDED', { value: fid, threshold: this.config.alertThresholds.FID });
            }
        });

        // Cumulative Layout Shift (CLS)
        this.observeWebVital('CLS', 'layout-shift', (entry) => {
            if (!entry.hadRecentInput) {
                const cls = entry.value;
                this.recordMetric('CLS', cls);
                
                if (cls > this.config.alertThresholds.CLS) {
                    this.triggerAlert('CLS_THRESHOLD_EXCEEDED', { value: cls, threshold: this.config.alertThresholds.CLS });
                }
            }
        });

        // Time to First Byte (TTFB)
        this.observeWebVital('TTFB', 'navigation', (entry) => {
            const ttfb = entry.responseStart - entry.requestStart;
            this.recordMetric('TTFB', ttfb);
            
            if (ttfb > this.config.alertThresholds.TTFB) {
                this.triggerAlert('TTFB_THRESHOLD_EXCEEDED', { value: ttfb, threshold: this.config.alertThresholds.TTFB });
            }
        });
    }

    observeWebVital(name, entryType, callback) {
        try {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    callback(entry);
                }
            });
            
            observer.observe({ entryTypes: [entryType] });
            this.observers.push({ name, observer });
            
        } catch (error) {
            console.warn(`Web Vital observer for ${name} not supported:`, error);
        }
    }

    /**
     * Custom Metrics Monitoring
     */
    initCustomMetrics() {
        // Search performance
        this.addCustomMetric('searchLatency', 'Search Response Time');
        this.addCustomMetric('renderTime', 'Component Render Time');
        this.addCustomMetric('cacheHitRate', 'Cache Hit Rate');
        this.addCustomMetric('apiResponseTime', 'API Response Time');
        
        // Product loading metrics
        this.addCustomMetric('productLoadTime', 'Product Load Time');
        this.addCustomMetric('imageLoadTime', 'Image Load Time');
        this.addCustomMetric('scrollPerformance', 'Scroll Performance');
    }

    addCustomMetric(name, description) {
        this.metrics.customMetrics.set(name, {
            name,
            description,
            values: [],
            average: 0,
            min: Infinity,
            max: -Infinity,
            count: 0
        });
    }

    recordCustomMetric(name, value, metadata = {}) {
        const metric = this.metrics.customMetrics.get(name);
        if (!metric) {
            console.warn(`Custom metric ${name} not found`);
            return;
        }

        metric.values.push({ value, timestamp: Date.now(), metadata });
        metric.count++;
        metric.min = Math.min(metric.min, value);
        metric.max = Math.max(metric.max, value);
        metric.average = metric.values.reduce((sum, item) => sum + item.value, 0) / metric.count;

        // Keep only recent values (last 100)
        if (metric.values.length > 100) {
            metric.values.shift();
        }
    }

    /**
     * Network Performance Monitoring
     */
    initNetworkMonitoring() {
        // Monitor fetch requests
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            const startTime = performance.now();
            const url = args[0];
            
            try {
                const response = await originalFetch(...args);
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                this.recordNetworkMetric({
                    url: typeof url === 'string' ? url : url.url,
                    method: args[1]?.method || 'GET',
                    status: response.status,
                    duration,
                    size: response.headers.get('content-length'),
                    cached: response.headers.get('x-cache') === 'HIT',
                    timestamp: Date.now()
                });
                
                return response;
            } catch (error) {
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                this.recordNetworkMetric({
                    url: typeof url === 'string' ? url : url.url,
                    method: args[1]?.method || 'GET',
                    status: 0,
                    duration,
                    error: error.message,
                    timestamp: Date.now()
                });
                
                throw error;
            }
        };

        // Monitor XMLHttpRequest
        const originalXHROpen = XMLHttpRequest.prototype.open;
        const originalXHRSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this._startTime = performance.now();
            this._method = method;
            this._url = url;
            return originalXHROpen.call(this, method, url, ...args);
        };
        
        XMLHttpRequest.prototype.send = function(...args) {
            this.addEventListener('loadend', () => {
                const duration = performance.now() - this._startTime;
                
                this.recordNetworkMetric({
                    url: this._url,
                    method: this._method,
                    status: this.status,
                    duration,
                    size: this.getResponseHeader('content-length'),
                    timestamp: Date.now()
                });
            });
            
            return originalXHRSend.call(this, ...args);
        };
    }

    recordNetworkMetric(metric) {
        this.metrics.networkMetrics.push(metric);
        
        // Keep only recent metrics (last 200)
        if (this.metrics.networkMetrics.length > 200) {
            this.metrics.networkMetrics.shift();
        }
        
        // Record as custom metric
        this.recordCustomMetric('apiResponseTime', metric.duration, {
            url: metric.url,
            status: metric.status,
            cached: metric.cached
        });
    }

    /**
     * Memory Monitoring
     */
    initMemoryMonitoring() {
        if (!('memory' in performance)) {
            console.warn('Memory API not supported');
            return;
        }

        // Monitor memory usage every 30 seconds
        setInterval(() => {
            const memory = performance.memory;
            const memoryMetric = {
                used: memory.usedJSHeapSize,
                total: memory.totalJSHeapSize,
                limit: memory.jsHeapSizeLimit,
                percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100,
                timestamp: Date.now()
            };
            
            this.metrics.memoryMetrics.push(memoryMetric);
            
            // Keep only recent metrics (last 100)
            if (this.metrics.memoryMetrics.length > 100) {
                this.metrics.memoryMetrics.shift();
            }
            
            // Check for memory threshold
            if (memoryMetric.percentage > this.config.alertThresholds.memoryUsage) {
                this.triggerAlert('MEMORY_THRESHOLD_EXCEEDED', {
                    percentage: memoryMetric.percentage,
                    threshold: this.config.alertThresholds.memoryUsage
                });
            }
            
            // Detect potential memory leaks
            this.detectMemoryLeaks();
            
        }, 30000);
    }

    detectMemoryLeaks() {
        const recentMetrics = this.metrics.memoryMetrics.slice(-10);
        if (recentMetrics.length < 10) return;
        
        // Check if memory usage is consistently increasing
        let increasingCount = 0;
        for (let i = 1; i < recentMetrics.length; i++) {
            if (recentMetrics[i].used > recentMetrics[i-1].used) {
                increasingCount++;
            }
        }
        
        // If memory increased in 8 out of 10 measurements, potential leak
        if (increasingCount >= 8) {
            this.triggerAlert('POTENTIAL_MEMORY_LEAK', {
                trend: 'increasing',
                measurements: recentMetrics.length,
                increases: increasingCount
            });
        }
    }

    /**
     * User Interaction Tracking
     */
    initUserInteractionTracking() {
        // Track click interactions
        document.addEventListener('click', (event) => {
            this.recordUserInteraction('click', {
                target: this.getElementSelector(event.target),
                timestamp: Date.now(),
                x: event.clientX,
                y: event.clientY
            });
        });

        // Track scroll performance
        let scrollStartTime = null;
        document.addEventListener('scroll', () => {
            if (!scrollStartTime) {
                scrollStartTime = performance.now();
                
                requestAnimationFrame(() => {
                    const scrollTime = performance.now() - scrollStartTime;
                    this.recordCustomMetric('scrollPerformance', scrollTime);
                    scrollStartTime = null;
                });
            }
        });

        // Track form interactions
        document.addEventListener('input', (event) => {
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
                this.recordUserInteraction('input', {
                    target: this.getElementSelector(event.target),
                    timestamp: Date.now()
                });
            }
        });
    }

    recordUserInteraction(type, data) {
        this.metrics.userInteractions.push({ type, ...data });
        
        // Keep only recent interactions (last 100)
        if (this.metrics.userInteractions.length > 100) {
            this.metrics.userInteractions.shift();
        }
    }

    getElementSelector(element) {
        if (element.id) return `#${element.id}`;
        if (element.className) return `.${element.className.split(' ')[0]}`;
        return element.tagName.toLowerCase();
    }

    /**
     * Error Tracking
     */
    initErrorTracking() {
        // JavaScript errors
        window.addEventListener('error', (event) => {
            this.recordError({
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack,
                timestamp: Date.now()
            });
        });

        // Promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.recordError({
                type: 'promise_rejection',
                message: event.reason?.message || 'Unhandled promise rejection',
                stack: event.reason?.stack,
                timestamp: Date.now()
            });
        });

        // Resource loading errors
        document.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.recordError({
                    type: 'resource',
                    message: `Failed to load ${event.target.tagName}`,
                    source: event.target.src || event.target.href,
                    timestamp: Date.now()
                });
            }
        }, true);
    }

    recordError(error) {
        this.metrics.errors.push(error);
        
        // Keep only recent errors (last 50)
        if (this.metrics.errors.length > 50) {
            this.metrics.errors.shift();
        }
        
        // Trigger alert for critical errors
        if (error.type === 'javascript' || error.type === 'promise_rejection') {
            this.triggerAlert('CRITICAL_ERROR', error);
        }
    }

    /**
     * Alerting System
     */
    triggerAlert(type, data) {
        if (!this.config.enableRealTimeAlerts) return;
        
        const alert = {
            type,
            data,
            timestamp: Date.now(),
            sessionId: this.sessionId,
            url: window.location.href,
            userAgent: navigator.userAgent
        };
        
        console.warn(`🚨 Performance Alert: ${type}`, data);
        
        // Send to monitoring service
        this.sendAlert(alert);
    }

    async sendAlert(alert) {
        // Skip sending alerts in local development
        if (window.location.hostname === '127.0.0.1' || window.location.hostname === 'localhost') {
            console.log('🚨 Alert (Local Dev):', alert.type, alert.data);
            return;
        }

        try {
            await fetch(`${this.config.reportingEndpoint}/alerts`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(alert)
            });
        } catch (error) {
            console.warn('Failed to send alert:', error);
        }
    }

    /**
     * Automated Reporting
     */
    startAutomatedReporting() {
        // Send reports every flush interval
        setInterval(() => {
            this.flushMetrics();
        }, this.config.flushInterval);
        
        // Send report on page unload
        window.addEventListener('beforeunload', () => {
            this.flushMetrics(true);
        });
        
        // Send report on visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.flushMetrics();
            }
        });
    }

    async flushMetrics(isBeacon = false) {
        const report = this.generateReport();

        // Skip sending in local development
        if (window.location.hostname === '127.0.0.1' || window.location.hostname === 'localhost') {
            console.log('📊 Performance Report (Local Dev):', {
                sessionDuration: report.sessionDuration,
                webVitals: report.webVitals,
                customMetrics: Object.keys(report.customMetrics).length,
                errors: report.errors.length
            });
            this.clearMetrics();
            return;
        }

        if (isBeacon && navigator.sendBeacon) {
            // Use sendBeacon for reliable delivery on page unload
            navigator.sendBeacon(
                this.config.reportingEndpoint,
                JSON.stringify(report)
            );
        } else {
            try {
                await fetch(this.config.reportingEndpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(report)
                });
            } catch (error) {
                console.warn('Failed to send performance report:', error);
            }
        }

        // Clear metrics after sending
        this.clearMetrics();
    }

    generateReport() {
        const now = Date.now();
        const sessionDuration = now - this.startTime;
        
        return {
            sessionId: this.sessionId,
            timestamp: now,
            sessionDuration,
            url: window.location.href,
            userAgent: navigator.userAgent,
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            },
            connection: this.getConnectionInfo(),
            webVitals: Object.fromEntries(this.metrics.webVitals),
            customMetrics: this.serializeCustomMetrics(),
            networkMetrics: this.metrics.networkMetrics.slice(),
            memoryMetrics: this.metrics.memoryMetrics.slice(),
            userInteractions: this.metrics.userInteractions.slice(),
            errors: this.metrics.errors.slice(),
            performance: {
                navigation: performance.getEntriesByType('navigation')[0],
                resources: performance.getEntriesByType('resource').slice(-20) // Last 20 resources
            }
        };
    }

    serializeCustomMetrics() {
        const serialized = {};
        for (const [name, metric] of this.metrics.customMetrics) {
            serialized[name] = {
                average: metric.average,
                min: metric.min,
                max: metric.max,
                count: metric.count,
                recent: metric.values.slice(-10) // Last 10 values
            };
        }
        return serialized;
    }

    getConnectionInfo() {
        if ('connection' in navigator) {
            const conn = navigator.connection;
            return {
                effectiveType: conn.effectiveType,
                downlink: conn.downlink,
                rtt: conn.rtt,
                saveData: conn.saveData
            };
        }
        return null;
    }

    clearMetrics() {
        this.metrics.networkMetrics = [];
        this.metrics.userInteractions = [];
        this.metrics.errors = [];
        
        // Keep some memory metrics for trend analysis
        if (this.metrics.memoryMetrics.length > 10) {
            this.metrics.memoryMetrics = this.metrics.memoryMetrics.slice(-10);
        }
    }

    /**
     * Public API
     */
    recordMetric(name, value, metadata = {}) {
        this.metrics.webVitals.set(name, { value, timestamp: Date.now(), metadata });
    }

    getMetrics() {
        return {
            webVitals: Object.fromEntries(this.metrics.webVitals),
            customMetrics: this.serializeCustomMetrics(),
            networkSummary: this.getNetworkSummary(),
            memorySummary: this.getMemorySummary(),
            errorSummary: this.getErrorSummary()
        };
    }

    getNetworkSummary() {
        const metrics = this.metrics.networkMetrics;
        if (metrics.length === 0) return null;
        
        const durations = metrics.map(m => m.duration);
        const successRate = metrics.filter(m => m.status >= 200 && m.status < 400).length / metrics.length;
        
        return {
            totalRequests: metrics.length,
            averageResponseTime: durations.reduce((a, b) => a + b, 0) / durations.length,
            successRate: successRate * 100,
            cacheHitRate: metrics.filter(m => m.cached).length / metrics.length * 100
        };
    }

    getMemorySummary() {
        const metrics = this.metrics.memoryMetrics;
        if (metrics.length === 0) return null;
        
        const latest = metrics[metrics.length - 1];
        const percentages = metrics.map(m => m.percentage);
        
        return {
            current: latest.percentage,
            average: percentages.reduce((a, b) => a + b, 0) / percentages.length,
            peak: Math.max(...percentages),
            trend: this.calculateTrend(percentages)
        };
    }

    getErrorSummary() {
        const errors = this.metrics.errors;
        const errorTypes = {};
        
        errors.forEach(error => {
            errorTypes[error.type] = (errorTypes[error.type] || 0) + 1;
        });
        
        return {
            totalErrors: errors.length,
            errorTypes,
            recentErrors: errors.slice(-5)
        };
    }

    calculateTrend(values) {
        if (values.length < 2) return 'stable';
        
        const recent = values.slice(-5);
        const older = values.slice(-10, -5);
        
        if (recent.length === 0 || older.length === 0) return 'stable';
        
        const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
        const olderAvg = older.reduce((a, b) => a + b, 0) / older.length;
        
        const change = ((recentAvg - olderAvg) / olderAvg) * 100;
        
        if (change > 10) return 'increasing';
        if (change < -10) return 'decreasing';
        return 'stable';
    }

    generateSessionId() {
        return `perf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    destroy() {
        // Clean up observers
        this.observers.forEach(({ observer }) => {
            observer.disconnect();
        });
        
        // Send final report
        this.flushMetrics(true);
        
        this.isMonitoring = false;
        console.log('📊 Performance Monitor destroyed');
    }
}

// Auto-initialize if in browser environment
if (typeof window !== 'undefined') {
    window.performanceMonitor = new EnterprisePerformanceMonitor({
        reportingEndpoint: '/api/performance',
        enableRealTimeAlerts: true,
        sampleRate: 1.0 // 100% for development, reduce in production
    });
    
    // Expose methods for manual tracking
    window.trackCustomMetric = (name, value, metadata) => {
        window.performanceMonitor.recordCustomMetric(name, value, metadata);
    };
}

console.log('📊 Enterprise Performance Monitor loaded');
