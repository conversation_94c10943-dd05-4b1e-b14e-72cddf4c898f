/* Dalilak Auto - Additional Styles */

/* Performance optimizations */
* {
    box-sizing: border-box;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading states */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Error states */
.error {
    color: #e74c3c;
    background: #fdf2f2;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #e74c3c;
}

/* Success states */
.success {
    color: #27ae60;
    background: #f2fdf2;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #27ae60;
}

/* Performance indicators */
.performance-good {
    color: #27ae60;
}

.performance-warning {
    color: #f39c12;
}

.performance-critical {
    color: #e74c3c;
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus indicators */
button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .auto-dark {
        background-color: #1a1a1a;
        color: #ffffff;
    }
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none;
    }
    
    .mobile-full {
        width: 100% !important;
    }
}

/* Tablet optimizations */
@media (min-width: 769px) and (max-width: 1024px) {
    .tablet-optimized {
        padding: 15px;
    }
}

/* Desktop optimizations */
@media (min-width: 1025px) {
    .desktop-optimized {
        max-width: 1200px;
        margin: 0 auto;
    }
}
