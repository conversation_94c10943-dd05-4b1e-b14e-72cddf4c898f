/**
 * Enterprise-Grade Product Management System
 * مستوى التطبيقات الكبيرة - أمازون، فيسبوك، جوجل
 * 
 * Features:
 * - Advanced Caching Strategy (Multi-level)
 * - Virtual Scrolling for 100k+ items
 * - Service Worker for offline support
 * - Real-time updates via WebSocket
 * - Advanced Search with ML-like scoring
 * - Performance monitoring & analytics
 * - Error tracking & recovery
 * - A/B Testing framework
 */

class EnterpriseProductSystem {
    constructor(config = {}) {
        // Core Configuration
        this.config = {
            apiBaseUrl: config.apiBaseUrl || 'https://dalilakauto.com/api/v1',
            pageSize: config.pageSize || 50,
            virtualScrollThreshold: config.virtualScrollThreshold || 100,
            cacheMaxSize: config.cacheMaxSize || 1000,
            searchDebounceMs: config.searchDebounceMs || 150,
            enableAnalytics: config.enableAnalytics !== false,
            enableServiceWorker: config.enableServiceWorker !== false,
            enableWebSocket: config.enableWebSocket !== false,
            ...config
        };

        // State Management (Redux-like)
        this.state = {
            products: new Map(),
            searchResults: new Map(),
            categories: new Map(),
            filters: {},
            loading: false,
            error: null,
            pagination: {
                currentPage: 1,
                totalPages: 0,
                totalItems: 0,
                hasMore: true
            },
            performance: {
                searchTimes: [],
                loadTimes: [],
                memoryUsage: []
            }
        };

        // Multi-level Caching System
        this.cache = {
            L1: new Map(), // Memory cache (fastest)
            L2: null,      // IndexedDB (persistent)
            L3: null       // Service Worker cache (network)
        };

        // Search Engine with ML-like features
        this.searchEngine = {
            index: new Map(),
            synonyms: new Map(),
            popularQueries: new Map(),
            userBehavior: new Map()
        };

        // Performance Monitoring
        this.performance = {
            metrics: new Map([
                ['loadTimes', []],
                ['searchLatency', []],
                ['memoryUsage', []],
                ['cacheHits', []],
                ['cacheMisses', []]
            ]),
            observers: [],
            startTime: performance.now()
        };

        // Error Tracking
        this.errorTracker = {
            errors: [],
            maxErrors: 100,
            reportEndpoint: config.errorReportEndpoint
        };

        // A/B Testing Framework
        this.abTesting = {
            experiments: new Map(),
            userSegment: this.getUserSegment()
        };

        this.init();
    }

    /**
     * Initialize Enterprise System
     */
    async init() {
        console.log('🏢 Initializing Enterprise Product System...');
        
        try {
            // Initialize all subsystems
            await Promise.all([
                this.initializeCache(),
                this.initializeSearchEngine(),
                this.initializePerformanceMonitoring(),
                this.initializeServiceWorker(),
                this.initializeWebSocket(),
                this.initializeAnalytics(),
                this.loadInitialData()
            ]);

            this.setupEventListeners();
            this.startBackgroundTasks();
            
            console.log('✅ Enterprise System initialized successfully');
            this.trackEvent('system_initialized', { timestamp: Date.now() });
            
        } catch (error) {
            this.handleError('initialization_failed', error);
            throw error;
        }
    }

    /**
     * Multi-level Cache System (L1: Memory, L2: IndexedDB, L3: Service Worker)
     */
    async initializeCache() {
        // L1 Cache (Memory) - Already initialized
        
        // L2 Cache (IndexedDB)
        this.cache.L2 = await this.initIndexedDB();
        
        // L3 Cache (Service Worker)
        if (this.config.enableServiceWorker && 'serviceWorker' in navigator) {
            this.cache.L3 = await this.initServiceWorker();
        }
        
        console.log('💾 Multi-level cache system initialized');
    }

    async initIndexedDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open('DalilaProductDB', 1);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => resolve(request.result);
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Products store
                if (!db.objectStoreNames.contains('products')) {
                    const productsStore = db.createObjectStore('products', { keyPath: 'id' });
                    productsStore.createIndex('category', 'category', { unique: false });
                    productsStore.createIndex('brand', 'brand', { unique: false });
                    productsStore.createIndex('price', 'price', { unique: false });
                }
                
                // Search cache store
                if (!db.objectStoreNames.contains('searchCache')) {
                    db.createObjectStore('searchCache', { keyPath: 'query' });
                }
                
                // Analytics store
                if (!db.objectStoreNames.contains('analytics')) {
                    db.createObjectStore('analytics', { keyPath: 'id', autoIncrement: true });
                }
            };
        });
    }

    /**
     * Advanced Search Engine with ML-like features
     */
    async initializeSearchEngine() {
        // Load search synonyms
        this.searchEngine.synonyms = new Map([
            ['سيارة', ['عربية', 'مركبة', 'أوتو']],
            ['قطعة', ['جزء', 'قطع', 'أجزاء']],
            ['محرك', ['موتور', 'انجن']],
            ['فرامل', ['بريك', 'مكابح']],
            // Add more synonyms...
        ]);

        // Load popular queries from analytics
        await this.loadPopularQueries();
        
        console.log('🔍 Advanced search engine initialized');
    }

    /**
     * Performance Monitoring (like Google Analytics)
     */
    initializePerformanceMonitoring() {
        // Core Web Vitals monitoring
        this.observePerformance('LCP', 'largest-contentful-paint');
        this.observePerformance('FID', 'first-input-delay');
        this.observePerformance('CLS', 'cumulative-layout-shift');
        
        // Custom metrics
        this.performance.metrics.set('searchLatency', []);
        this.performance.metrics.set('renderTime', []);
        this.performance.metrics.set('memoryUsage', []);
        
        // Memory monitoring
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                if (this.performance.metrics.has('memoryUsage')) {
                    this.performance.metrics.get('memoryUsage').push({
                        used: memory.usedJSHeapSize,
                        total: memory.totalJSHeapSize,
                        limit: memory.jsHeapSizeLimit,
                        timestamp: Date.now()
                    });
                } else {
                    this.performance.metrics.set('memoryUsage', [{
                        used: memory.usedJSHeapSize,
                        total: memory.totalJSHeapSize,
                        limit: memory.jsHeapSizeLimit,
                        timestamp: Date.now()
                    }]);
                }
            }, 30000); // Every 30 seconds
        }
        
        console.log('📊 Performance monitoring initialized');
    }

    observePerformance(name, entryType) {
        const observer = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
                this.performance.metrics.set(name, entry.value);
                this.trackEvent('performance_metric', {
                    metric: name,
                    value: entry.value,
                    timestamp: Date.now()
                });
            }
        });
        
        try {
            observer.observe({ entryTypes: [entryType] });
            this.performance.observers.push(observer);
        } catch (e) {
            console.warn(`Performance observer for ${name} not supported`);
        }
    }

    /**
     * Service Worker for offline support
     */
    async initServiceWorker() {
        if (!('serviceWorker' in navigator)) return null;

        try {
            const registration = await navigator.serviceWorker.register('/sw-enterprise.js');
            console.log('🔧 Service Worker registered');
            return registration;
        } catch (error) {
            console.warn('Service Worker registration failed:', error);
            return null;
        }
    }

    /**
     * Service Worker for offline support (alias)
     */
    async initializeServiceWorker() {
        return this.initServiceWorker();
    }

    /**
     * WebSocket for real-time updates
     */
    async initializeWebSocket() {
        if (!this.config.enableWebSocket) return;

        try {
            this.websocket = new WebSocket('wss://dalilakauto.com/ws');

            this.websocket.onopen = () => {
                console.log('🔗 WebSocket connected');
                this.trackEvent('websocket_connected');
            };

            this.websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleRealtimeUpdate(data);
            };

            this.websocket.onclose = () => {
                console.log('🔗 WebSocket disconnected, attempting reconnect...');
                setTimeout(() => this.initializeWebSocket(), 5000);
            };

        } catch (error) {
            console.warn('WebSocket initialization failed:', error);
        }
    }

    /**
     * WebSocket for real-time updates (alias)
     */
    async initWebSocket() {
        return this.initializeWebSocket();
    }

    /**
     * Analytics System (like Google Analytics)
     */
    initializeAnalytics() {
        if (!this.config.enableAnalytics) return;

        this.analytics = {
            sessionId: this.generateSessionId(),
            userId: this.getUserId(),
            events: [],
            batchSize: 10,
            flushInterval: 30000 // 30 seconds
        };

        // Auto-flush analytics
        setInterval(() => {
            this.flushAnalytics();
        }, this.analytics.flushInterval);

        console.log('📈 Analytics system initialized');
    }

    /**
     * Analytics System (alias)
     */
    initAnalytics() {
        return this.initializeAnalytics();
    }

    /**
     * Virtual Scrolling for massive datasets
     */
    createVirtualScrollContainer(container, items, itemHeight = 100) {
        const virtualScroller = {
            container,
            items,
            itemHeight,
            visibleStart: 0,
            visibleEnd: 0,
            scrollTop: 0,
            containerHeight: container.clientHeight,
            totalHeight: items.length * itemHeight,
            
            update() {
                const scrollTop = container.scrollTop;
                const visibleStart = Math.floor(scrollTop / itemHeight);
                const visibleEnd = Math.min(
                    visibleStart + Math.ceil(this.containerHeight / itemHeight) + 1,
                    items.length
                );
                
                if (visibleStart !== this.visibleStart || visibleEnd !== this.visibleEnd) {
                    this.visibleStart = visibleStart;
                    this.visibleEnd = visibleEnd;
                    this.render();
                }
            },
            
            render() {
                const fragment = document.createDocumentFragment();
                
                // Add spacer for items before visible area
                if (this.visibleStart > 0) {
                    const spacer = document.createElement('div');
                    spacer.style.height = `${this.visibleStart * itemHeight}px`;
                    fragment.appendChild(spacer);
                }
                
                // Render visible items
                for (let i = this.visibleStart; i < this.visibleEnd; i++) {
                    const item = this.createItemElement(items[i], i);
                    fragment.appendChild(item);
                }
                
                // Add spacer for items after visible area
                if (this.visibleEnd < items.length) {
                    const spacer = document.createElement('div');
                    spacer.style.height = `${(items.length - this.visibleEnd) * itemHeight}px`;
                    fragment.appendChild(spacer);
                }
                
                container.innerHTML = '';
                container.appendChild(fragment);
            },
            
            createItemElement(item, index) {
                const element = document.createElement('div');
                element.className = 'virtual-item';
                element.style.height = `${itemHeight}px`;
                element.innerHTML = this.renderItem(item, index);
                return element;
            },
            
            renderItem(item, index) {
                return `
                    <div class="product-card-virtual" data-id="${item.id}">
                        <img src="${item.image}" alt="${item.name}" loading="lazy">
                        <h3>${item.name}</h3>
                        <p class="price">${item.price} ريال</p>
                        <button onclick="addToCart(${item.id})">إضافة للسلة</button>
                    </div>
                `;
            }
        };
        
        // Setup scroll listener
        container.addEventListener('scroll', () => {
            virtualScroller.update();
        });
        
        // Initial render
        virtualScroller.update();
        
        return virtualScroller;
    }

    /**
     * Advanced Search with ML-like scoring
     */
    async performAdvancedSearch(query, options = {}) {
        const startTime = performance.now();
        
        try {
            // Normalize query
            const normalizedQuery = this.normalizeSearchQuery(query);
            
            // Check cache first
            const cacheKey = `search:${normalizedQuery}:${JSON.stringify(options)}`;
            let results = await this.getCachedResults(cacheKey);
            
            if (!results) {
                // Perform search with scoring
                results = await this.executeSearch(normalizedQuery, options);
                
                // Cache results
                await this.cacheResults(cacheKey, results);
            }
            
            // Track search performance
            const searchTime = performance.now() - startTime;
            if (this.performance.metrics.has('searchLatency')) {
                this.performance.metrics.get('searchLatency').push(searchTime);
            } else {
                this.performance.metrics.set('searchLatency', [searchTime]);
            }
            
            // Track search analytics
            this.trackEvent('search_performed', {
                query: normalizedQuery,
                resultsCount: results.length,
                searchTime,
                cached: !!results.fromCache
            });
            
            return results;
            
        } catch (error) {
            this.handleError('search_failed', error, { query, options });
            throw error;
        }
    }

    normalizeSearchQuery(query) {
        return query
            .toLowerCase()
            .trim()
            .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\w\s]/g, ' ')
            .replace(/\s+/g, ' ');
    }

    async executeSearch(query, options) {
        const words = query.split(' ').filter(word => word.length > 1);
        const results = new Map();
        
        // Search in products
        for (const [id, product] of this.state.products) {
            const score = this.calculateSearchScore(product, words, options);
            if (score > 0) {
                results.set(id, { ...product, searchScore: score });
            }
        }
        
        // Sort by score
        return Array.from(results.values())
            .sort((a, b) => b.searchScore - a.searchScore)
            .slice(0, options.limit || 50);
    }

    calculateSearchScore(product, words, options) {
        let score = 0;
        const text = `${product.name} ${product.description || ''} ${product.sku || ''}`.toLowerCase();
        
        words.forEach(word => {
            // Exact match in name (highest score)
            if (product.name.toLowerCase().includes(word)) {
                score += 10;
            }
            
            // Exact match in description
            if (product.description && product.description.toLowerCase().includes(word)) {
                score += 5;
            }
            
            // Fuzzy match
            if (this.fuzzyMatch(text, word)) {
                score += 2;
            }
            
            // Synonym match
            const synonyms = this.searchEngine.synonyms.get(word) || [];
            synonyms.forEach(synonym => {
                if (text.includes(synonym)) {
                    score += 3;
                }
            });
        });
        
        // Boost popular products
        if (product.isPopular) score *= 1.2;
        if (product.isNew) score *= 1.1;
        
        return score;
    }

    fuzzyMatch(text, word) {
        // Simple fuzzy matching algorithm
        const threshold = 0.8;
        const words = text.split(' ');
        
        return words.some(textWord => {
            if (textWord.length === 0 || word.length === 0) return false;
            
            const longer = textWord.length > word.length ? textWord : word;
            const shorter = textWord.length > word.length ? word : textWord;
            
            const editDistance = this.levenshteinDistance(longer, shorter);
            const similarity = (longer.length - editDistance) / longer.length;
            
            return similarity >= threshold;
        });
    }

    levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }

    /**
     * Error Tracking & Recovery
     */
    handleError(type, error, context = {}) {
        const errorInfo = {
            type,
            message: error.message,
            stack: error.stack,
            context,
            timestamp: Date.now(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };
        
        this.errorTracker.errors.push(errorInfo);
        
        // Keep only recent errors
        if (this.errorTracker.errors.length > this.errorTracker.maxErrors) {
            this.errorTracker.errors.shift();
        }
        
        // Report to server if endpoint configured
        if (this.errorTracker.reportEndpoint) {
            this.reportError(errorInfo);
        }
        
        console.error(`[${type}]`, error, context);
    }

    async reportError(errorInfo) {
        try {
            await fetch(this.errorTracker.reportEndpoint, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(errorInfo)
            });
        } catch (e) {
            console.warn('Failed to report error:', e);
        }
    }

    /**
     * A/B Testing Framework
     */
    getUserSegment() {
        const userId = this.getUserId();
        const hash = this.simpleHash(userId);
        return hash % 100; // 0-99
    }

    isInExperiment(experimentName, percentage = 50) {
        return this.abTesting.userSegment < percentage;
    }

    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash);
    }

    /**
     * Analytics & Event Tracking
     */
    trackEvent(eventName, properties = {}) {
        if (!this.config.enableAnalytics) return;
        
        const event = {
            name: eventName,
            properties: {
                ...properties,
                sessionId: this.analytics.sessionId,
                userId: this.analytics.userId,
                timestamp: Date.now(),
                url: window.location.href,
                userAgent: navigator.userAgent
            }
        };
        
        this.analytics.events.push(event);
        
        // Auto-flush if batch is full
        if (this.analytics.events.length >= this.analytics.batchSize) {
            this.flushAnalytics();
        }
    }

    async flushAnalytics() {
        if (this.analytics.events.length === 0) return;
        
        const events = [...this.analytics.events];
        this.analytics.events = [];
        
        try {
            // Store in IndexedDB
            if (this.cache.L2) {
                const transaction = this.cache.L2.transaction(['analytics'], 'readwrite');
                const store = transaction.objectStore('analytics');
                
                events.forEach(event => {
                    store.add(event);
                });
            }
            
            // Send to server (if online and not local dev)
            if (navigator.onLine && this.config.analyticsEndpoint &&
                window.location.hostname !== '127.0.0.1' &&
                window.location.hostname !== 'localhost') {
                await fetch(this.config.analyticsEndpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ events })
                });
            } else if (window.location.hostname === '127.0.0.1' || window.location.hostname === 'localhost') {
                console.log('📊 Analytics (Local Dev):', events.length, 'events stored locally');
            }
            
        } catch (error) {
            console.warn('Failed to flush analytics:', error);
            // Re-add events to queue
            this.analytics.events.unshift(...events);
        }
    }

    /**
     * Utility Methods
     */
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    getUserId() {
        let userId = localStorage.getItem('dalila_user_id');
        if (!userId) {
            userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            localStorage.setItem('dalila_user_id', userId);
        }
        return userId;
    }

    async getCachedResults(key) {
        // Try L1 cache first
        if (this.cache.L1.has(key)) {
            return { ...this.cache.L1.get(key), fromCache: true };
        }
        
        // Try L2 cache (IndexedDB)
        if (this.cache.L2) {
            try {
                const transaction = this.cache.L2.transaction(['searchCache'], 'readonly');
                const store = transaction.objectStore('searchCache');
                const request = store.get(key);
                
                return new Promise((resolve) => {
                    request.onsuccess = () => {
                        if (request.result) {
                            // Also cache in L1 for faster access
                            this.cache.L1.set(key, request.result.data);
                            resolve({ ...request.result.data, fromCache: true });
                        } else {
                            resolve(null);
                        }
                    };
                    request.onerror = () => resolve(null);
                });
            } catch (error) {
                console.warn('L2 cache read failed:', error);
                return null;
            }
        }
        
        return null;
    }

    async cacheResults(key, results) {
        // Cache in L1
        this.cache.L1.set(key, results);
        
        // Clean L1 cache if too large
        if (this.cache.L1.size > this.config.cacheMaxSize) {
            const firstKey = this.cache.L1.keys().next().value;
            this.cache.L1.delete(firstKey);
        }
        
        // Cache in L2 (IndexedDB)
        if (this.cache.L2) {
            try {
                const transaction = this.cache.L2.transaction(['searchCache'], 'readwrite');
                const store = transaction.objectStore('searchCache');
                
                store.put({
                    query: key,
                    data: results,
                    timestamp: Date.now()
                });
            } catch (error) {
                console.warn('L2 cache write failed:', error);
            }
        }
    }

    /**
     * Background Tasks
     */
    startBackgroundTasks() {
        // Cleanup old cache entries
        setInterval(() => {
            this.cleanupCache();
        }, 300000); // Every 5 minutes
        
        // Sync with server
        setInterval(() => {
            this.syncWithServer();
        }, 600000); // Every 10 minutes
        
        // Performance monitoring
        setInterval(() => {
            this.collectPerformanceMetrics();
        }, 60000); // Every minute
    }

    async cleanupCache() {
        const now = Date.now();
        const maxAge = 3600000; // 1 hour
        
        // Clean L2 cache
        if (this.cache.L2) {
            try {
                const transaction = this.cache.L2.transaction(['searchCache'], 'readwrite');
                const store = transaction.objectStore('searchCache');
                const request = store.openCursor();
                
                request.onsuccess = (event) => {
                    const cursor = event.target.result;
                    if (cursor) {
                        if (now - cursor.value.timestamp > maxAge) {
                            cursor.delete();
                        }
                        cursor.continue();
                    }
                };
            } catch (error) {
                console.warn('Cache cleanup failed:', error);
            }
        }
    }

    async syncWithServer() {
        if (!navigator.onLine) return;
        
        try {
            // Sync popular queries
            const response = await fetch(`${this.config.apiBaseUrl}/analytics/popular-queries`);
            if (response.ok) {
                const data = await response.json();
                this.searchEngine.popularQueries = new Map(data.queries);
            }
        } catch (error) {
            console.warn('Server sync failed:', error);
        }
    }

    collectPerformanceMetrics() {
        const metrics = {
            timestamp: Date.now(),
            cacheHitRate: this.calculateCacheHitRate(),
            searchLatency: this.getAverageSearchLatency(),
            memoryUsage: this.getCurrentMemoryUsage(),
            errorRate: this.calculateErrorRate()
        };
        
        this.trackEvent('performance_metrics', metrics);
    }

    calculateCacheHitRate() {
        // Implementation for cache hit rate calculation
        return 0.85; // Placeholder
    }

    getAverageSearchLatency() {
        const latencies = this.performance.metrics.get('searchLatency') || [];
        if (latencies.length === 0) return 0;
        
        const sum = latencies.reduce((a, b) => a + b, 0);
        return sum / latencies.length;
    }

    getCurrentMemoryUsage() {
        if ('memory' in performance) {
            return {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                percentage: (performance.memory.usedJSHeapSize / performance.memory.totalJSHeapSize) * 100
            };
        }
        return null;
    }

    calculateErrorRate() {
        const recentErrors = this.errorTracker.errors.filter(
            error => Date.now() - error.timestamp < 3600000 // Last hour
        );
        return recentErrors.length;
    }

    /**
     * Public API Methods
     */
    async loadProducts(page = 1, options = {}) {
        const startTime = performance.now();
        
        try {
            this.state.loading = true;
            
            const response = await fetch(
                `${this.config.apiBaseUrl}/ecommerce/products?page=${page}&per_page=${this.config.pageSize}`,
                { 
                    headers: { 'Accept': 'application/json' },
                    ...options 
                }
            );
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.error === false && data.data) {
                // Update state
                data.data.forEach(product => {
                    this.state.products.set(product.id, product);
                });
                
                this.state.pagination = {
                    currentPage: page,
                    totalPages: data.meta?.last_page || 1,
                    totalItems: data.meta?.total || data.data.length,
                    hasMore: page < (data.meta?.last_page || 1)
                };
                
                // Track performance
                const loadTime = performance.now() - startTime;
                if (this.performance.metrics.has('loadTimes')) {
                    this.performance.metrics.get('loadTimes').push(loadTime);
                } else {
                    this.performance.metrics.set('loadTimes', [loadTime]);
                }
                
                this.trackEvent('products_loaded', {
                    page,
                    count: data.data.length,
                    loadTime,
                    totalProducts: this.state.products.size
                });
                
                return data.data;
            } else {
                throw new Error('Invalid response format');
            }
            
        } catch (error) {
            this.handleError('load_products_failed', error, { page, options });
            throw error;
        } finally {
            this.state.loading = false;
        }
    }

    async search(query, options = {}) {
        return this.performAdvancedSearch(query, options);
    }

    getPerformanceReport() {
        return {
            system: 'Enterprise Product Management System',
            version: '1.0.0',
            uptime: Date.now() - this.performance.startTime,
            metrics: {
                totalProducts: this.state.products.size,
                cacheSize: {
                    L1: this.cache.L1.size,
                    L2: 'IndexedDB',
                    L3: 'Service Worker'
                },
                searchIndex: this.searchEngine.index.size,
                averageSearchLatency: this.getAverageSearchLatency(),
                memoryUsage: this.getCurrentMemoryUsage(),
                errorRate: this.calculateErrorRate(),
                cacheHitRate: this.calculateCacheHitRate()
            },
            features: {
                virtualScrolling: true,
                multiLevelCache: true,
                advancedSearch: true,
                realTimeUpdates: !!this.websocket,
                offlineSupport: !!this.cache.L3,
                analytics: this.config.enableAnalytics,
                abTesting: true,
                errorTracking: true
            }
        };
    }

    // Event handlers
    setupEventListeners() {
        // Handle online/offline events
        window.addEventListener('online', () => {
            this.trackEvent('connection_restored');
            this.syncWithServer();
        });
        
        window.addEventListener('offline', () => {
            this.trackEvent('connection_lost');
        });
        
        // Handle visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.trackEvent('page_hidden');
                this.flushAnalytics();
            } else {
                this.trackEvent('page_visible');
            }
        });
        
        // Handle beforeunload
        window.addEventListener('beforeunload', () => {
            this.flushAnalytics();
        });
    }

    handleRealtimeUpdate(data) {
        switch (data.type) {
            case 'product_updated':
                if (this.state.products.has(data.productId)) {
                    this.state.products.set(data.productId, { ...this.state.products.get(data.productId), ...data.updates });
                    this.trackEvent('product_updated_realtime', { productId: data.productId });
                }
                break;
                
            case 'new_product':
                this.state.products.set(data.product.id, data.product);
                this.trackEvent('new_product_realtime', { productId: data.product.id });
                break;
                
            case 'product_deleted':
                this.state.products.delete(data.productId);
                this.trackEvent('product_deleted_realtime', { productId: data.productId });
                break;
        }
    }

    async loadInitialData() {
        // Load first page of products
        await this.loadProducts(1);
        
        // Load popular queries
        await this.loadPopularQueries();
    }

    async loadPopularQueries() {
        try {
            if (this.cache.L2) {
                const transaction = this.cache.L2.transaction(['analytics'], 'readonly');
                const store = transaction.objectStore('analytics');
                const request = store.getAll();
                
                request.onsuccess = () => {
                    const events = request.result.filter(event => event.name === 'search_performed');
                    const queryCount = new Map();
                    
                    events.forEach(event => {
                        const query = event.properties.query;
                        queryCount.set(query, (queryCount.get(query) || 0) + 1);
                    });
                    
                    this.searchEngine.popularQueries = queryCount;
                };
            }
        } catch (error) {
            console.warn('Failed to load popular queries:', error);
        }
    }
}

// Export for global use
window.EnterpriseProductSystem = EnterpriseProductSystem;

// Auto-initialize if in browser environment
if (typeof window !== 'undefined') {
    window.enterpriseSystem = new EnterpriseProductSystem({
        apiBaseUrl: 'https://dalilakauto.com/api/v1',
        enableAnalytics: true,
        enableServiceWorker: true,
        enableWebSocket: false, // Enable when WebSocket server is ready
        analyticsEndpoint: '/api/analytics',
        errorReportEndpoint: '/api/errors'
    });
}

console.log('🏢 Enterprise Product Management System loaded');
