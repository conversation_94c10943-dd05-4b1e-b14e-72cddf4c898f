<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام المحسن - Dalilak Auto</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
        }

        .test-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .test-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
        }

        .test-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }

        .status-pending {
            background: #ffeaa7;
            color: #d63031;
        }

        .status-running {
            background: #74b9ff;
            color: white;
        }

        .status-passed {
            background: #00b894;
            color: white;
        }

        .status-failed {
            background: #e17055;
            color: white;
        }

        .test-body {
            padding: 20px;
        }

        .test-result {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 200px;
            overflow-y: auto;
        }

        .controls {
            text-align: center;
            margin-bottom: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00b894, #00cec9);
            width: 0%;
            transition: width 0.5s ease;
        }

        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-success { color: #00b894; }
        .log-error { color: #e17055; }
        .log-warning { color: #fdcb6e; }
        .log-info { color: #74b9ff; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .loading {
            animation: pulse 1.5s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار النظام المحسن</h1>
            <p>فحص شامل لنظام إدارة المنتجات المحسن - Dalilak Auto</p>
        </div>

        <div class="content">
            <div class="controls">
                <button class="btn" onclick="runAllTests()" id="runTestsBtn">
                    🚀 تشغيل جميع الاختبارات
                </button>
                <button class="btn" onclick="clearResults()" id="clearBtn">
                    🗑️ مسح النتائج
                </button>
                <button class="btn" onclick="exportResults()" id="exportBtn">
                    📊 تصدير النتائج
                </button>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <div id="testResults">
                <!-- نتائج الاختبارات ستظهر هنا -->
            </div>

            <div class="stats-grid" id="statsGrid" style="display: none;">
                <div class="stat-card">
                    <div class="stat-value" id="totalTests">0</div>
                    <div class="stat-label">إجمالي الاختبارات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="passedTests">0</div>
                    <div class="stat-label">اختبارات ناجحة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="failedTests">0</div>
                    <div class="stat-label">اختبارات فاشلة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="successRate">0%</div>
                    <div class="stat-label">معدل النجاح</div>
                </div>
            </div>

            <div class="log-container" id="logContainer" style="display: none;">
                <div style="text-align: center; margin-bottom: 15px; font-weight: bold;">
                    📋 سجل الاختبارات التفصيلي
                </div>
                <div id="logEntries"></div>
            </div>
        </div>
    </div>

    <script>
        let testResults = [];
        let currentTestIndex = 0;

        const tests = [
            {
                name: 'فحص وجود النظام المحسن',
                description: 'التأكد من وجود optimizedProductManager',
                func: 'testOptimizedSystemExists'
            },
            {
                name: 'فحص التهيئة',
                description: 'التأكد من تهيئة النظام بشكل صحيح',
                func: 'testInitialization'
            },
            {
                name: 'فحص تحميل المنتجات',
                description: 'التأكد من تحميل المنتجات بنجاح',
                func: 'testProductLoading'
            },
            {
                name: 'فحص نظام الكاش',
                description: 'التأكد من عمل نظام التخزين المؤقت',
                func: 'testCacheSystem'
            },
            {
                name: 'فحص فهرس البحث',
                description: 'التأكد من بناء فهرس البحث',
                func: 'testSearchIndex'
            },
            {
                name: 'فحص البحث المحلي',
                description: 'اختبار وظيفة البحث المحلي',
                func: 'testLocalSearch'
            },
            {
                name: 'فحص استخراج الكلمات',
                description: 'اختبار استخراج الكلمات المفتاحية',
                func: 'testKeywordExtraction'
            },
            {
                name: 'فحص الأداء',
                description: 'قياس سرعة البحث والاستجابة',
                func: 'testPerformance'
            },
            {
                name: 'فحص التكامل',
                description: 'التأكد من التكامل مع النظام القديم',
                func: 'testLegacyIntegration'
            },
            {
                name: 'فحص الدوال المحسنة',
                description: 'التأكد من تحديث الدوال الرئيسية',
                func: 'testOptimizedFunctions'
            }
        ];

        function createTestSection(test, index) {
            return `
                <div class="test-section" id="test-${index}">
                    <div class="test-header">
                        <div class="test-title">${test.name}</div>
                        <div class="test-status status-pending" id="status-${index}">في الانتظار</div>
                    </div>
                    <div class="test-body">
                        <p>${test.description}</p>
                        <div class="test-result" id="result-${index}" style="display: none;"></div>
                    </div>
                </div>
            `;
        }

        function initializeTests() {
            const container = document.getElementById('testResults');
            container.innerHTML = tests.map((test, index) => createTestSection(test, index)).join('');
        }

        function updateProgress(current, total) {
            const percentage = (current / total) * 100;
            document.getElementById('progressFill').style.width = percentage + '%';
        }

        function updateStats() {
            const total = testResults.length;
            const passed = testResults.filter(r => r.passed).length;
            const failed = total - passed;
            const rate = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;

            document.getElementById('totalTests').textContent = total;
            document.getElementById('passedTests').textContent = passed;
            document.getElementById('failedTests').textContent = failed;
            document.getElementById('successRate').textContent = rate + '%';

            document.getElementById('statsGrid').style.display = 'grid';
        }

        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('logEntries');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(entry);
            
            // التمرير للأسفل
            document.getElementById('logContainer').scrollTop = document.getElementById('logContainer').scrollHeight;
        }

        async function runSingleTest(testIndex) {
            const test = tests[testIndex];
            const statusEl = document.getElementById(`status-${testIndex}`);
            const resultEl = document.getElementById(`result-${testIndex}`);

            // تحديث الحالة
            statusEl.textContent = 'قيد التشغيل';
            statusEl.className = 'test-status status-running loading';

            logMessage(`بدء اختبار: ${test.name}`, 'info');

            try {
                // محاولة تشغيل الاختبار
                let result;
                if (window.testOptimizedSystem && window.testOptimizedSystem[test.func]) {
                    result = await window.testOptimizedSystem[test.func]();
                } else {
                    throw new Error(`دالة الاختبار ${test.func} غير موجودة`);
                }

                // تحديث النتيجة
                const passed = result === true;
                statusEl.textContent = passed ? 'نجح' : 'فشل';
                statusEl.className = `test-status ${passed ? 'status-passed' : 'status-failed'}`;

                resultEl.style.display = 'block';
                resultEl.innerHTML = `
                    <strong>النتيجة:</strong> ${passed ? '✅ نجح' : '❌ فشل'}<br>
                    <strong>الوقت:</strong> ${new Date().toLocaleTimeString('ar-SA')}
                `;

                testResults[testIndex] = { name: test.name, passed, error: null };
                logMessage(`${test.name}: ${passed ? 'نجح' : 'فشل'}`, passed ? 'success' : 'error');

            } catch (error) {
                statusEl.textContent = 'خطأ';
                statusEl.className = 'test-status status-failed';

                resultEl.style.display = 'block';
                resultEl.innerHTML = `
                    <strong>خطأ:</strong> ${error.message}<br>
                    <strong>الوقت:</strong> ${new Date().toLocaleTimeString('ar-SA')}
                `;

                testResults[testIndex] = { name: test.name, passed: false, error: error.message };
                logMessage(`${test.name}: خطأ - ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            const runBtn = document.getElementById('runTestsBtn');
            runBtn.disabled = true;
            runBtn.textContent = '⏳ جاري التشغيل...';

            // إعادة تعيين النتائج
            testResults = [];
            currentTestIndex = 0;

            // إظهار سجل الأحداث
            document.getElementById('logContainer').style.display = 'block';
            document.getElementById('logEntries').innerHTML = '';

            logMessage('بدء تشغيل جميع الاختبارات...', 'info');

            // تشغيل الاختبارات واحداً تلو الآخر
            for (let i = 0; i < tests.length; i++) {
                await runSingleTest(i);
                updateProgress(i + 1, tests.length);
                
                // تأخير قصير بين الاختبارات
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // تحديث الإحصائيات
            updateStats();

            // إعادة تفعيل الزر
            runBtn.disabled = false;
            runBtn.textContent = '🚀 تشغيل جميع الاختبارات';

            logMessage('انتهاء جميع الاختبارات', 'success');
        }

        function clearResults() {
            testResults = [];
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('statsGrid').style.display = 'none';
            document.getElementById('logContainer').style.display = 'none';
            initializeTests();
        }

        function exportResults() {
            if (testResults.length === 0) {
                alert('لا توجد نتائج للتصدير');
                return;
            }

            const report = {
                timestamp: new Date().toISOString(),
                total: testResults.length,
                passed: testResults.filter(r => r.passed).length,
                failed: testResults.filter(r => !r.passed).length,
                results: testResults
            };

            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `test-results-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeTests();
            
            // تحميل ملف الاختبار
            const script = document.createElement('script');
            script.src = 'test_optimized_system.js';
            script.onload = function() {
                console.log('✅ تم تحميل ملف الاختبار');
            };
            script.onerror = function() {
                console.error('❌ فشل في تحميل ملف الاختبار');
            };
            document.head.appendChild(script);
        });
    </script>
</body>
</html>
