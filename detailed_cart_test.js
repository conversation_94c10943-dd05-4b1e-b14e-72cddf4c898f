/**
 * فحص مفصل لوظائف التسوق والسلة
 * Detailed Cart and Shopping Functions Test
 */

console.log('🛒 بدء فحص مفصل لوظائف التسوق...');

// فحص 1: وظائف السلة الأساسية
function testBasicCartFunctions() {
    console.log('\n1️⃣ فحص وظائف السلة الأساسية:');
    
    const cartFunctions = [
        'addToCart',
        'removeFromCart', 
        'updateCartQuantity',
        'clearCart',
        'getCartTotal',
        'displayCart'
    ];
    
    let workingFunctions = 0;
    cartFunctions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ ${funcName}() - موجودة`);
            workingFunctions++;
        } else {
            console.log(`❌ ${funcName}() - مفقودة`);
        }
    });
    
    return workingFunctions === cartFunctions.length;
}

// فحص 2: تخزين بيانات السلة
function testCartStorage() {
    console.log('\n2️⃣ فحص تخزين بيانات السلة:');
    
    try {
        // فحص localStorage
        const cart = JSON.parse(localStorage.getItem('cart') || '[]');
        console.log(`📊 عدد المنتجات المحفوظة: ${cart.length}`);
        
        // اختبار إضافة منتج تجريبي
        const testProduct = {
            id: 'test-123',
            name: 'منتج تجريبي',
            price: 100,
            quantity: 1,
            image: 'test.jpg'
        };
        
        // حفظ السلة الحالية
        const originalCart = [...cart];
        
        // إضافة المنتج التجريبي
        cart.push(testProduct);
        localStorage.setItem('cart', JSON.stringify(cart));
        
        // التحقق من الحفظ
        const savedCart = JSON.parse(localStorage.getItem('cart') || '[]');
        const testProductFound = savedCart.find(item => item.id === 'test-123');
        
        if (testProductFound) {
            console.log('✅ تخزين السلة يعمل بشكل صحيح');
            
            // استعادة السلة الأصلية
            localStorage.setItem('cart', JSON.stringify(originalCart));
            return true;
        } else {
            console.log('❌ فشل في تخزين السلة');
            return false;
        }
        
    } catch (error) {
        console.log(`❌ خطأ في تخزين السلة: ${error.message}`);
        return false;
    }
}

// فحص 3: حساب المجموع
function testCartCalculations() {
    console.log('\n3️⃣ فحص حساب المجموع:');
    
    try {
        const cart = JSON.parse(localStorage.getItem('cart') || '[]');
        
        if (cart.length === 0) {
            console.log('ℹ️ السلة فارغة - لا يمكن اختبار الحسابات');
            return true;
        }
        
        // حساب المجموع يدوياً
        let manualTotal = 0;
        cart.forEach(item => {
            const price = parseFloat(item.price) || 0;
            const quantity = parseInt(item.quantity) || 1;
            manualTotal += price * quantity;
        });
        
        console.log(`💰 المجموع المحسوب يدوياً: ${manualTotal.toFixed(2)}`);
        
        // فحص دالة getCartTotal إذا كانت موجودة
        if (typeof getCartTotal === 'function') {
            const calculatedTotal = getCartTotal();
            console.log(`💰 المجموع من الدالة: ${calculatedTotal}`);
            
            if (Math.abs(manualTotal - calculatedTotal) < 0.01) {
                console.log('✅ حساب المجموع صحيح');
                return true;
            } else {
                console.log('❌ خطأ في حساب المجموع');
                return false;
            }
        } else {
            console.log('⚠️ دالة getCartTotal غير موجودة');
            return false;
        }
        
    } catch (error) {
        console.log(`❌ خطأ في حساب المجموع: ${error.message}`);
        return false;
    }
}

// فحص 4: عداد السلة
function testCartBadge() {
    console.log('\n4️⃣ فحص عداد السلة:');
    
    const cartBadges = [
        'cartBadge',
        'cartCount', 
        'headerCartBadge'
    ];
    
    let workingBadges = 0;
    cartBadges.forEach(badgeId => {
        const badge = document.getElementById(badgeId);
        if (badge) {
            console.log(`✅ ${badgeId} - موجود`);
            console.log(`   النص الحالي: "${badge.textContent}"`);
            workingBadges++;
        } else {
            console.log(`❌ ${badgeId} - مفقود`);
        }
    });
    
    // فحص تحديث العداد
    const cart = JSON.parse(localStorage.getItem('cart') || '[]');
    const expectedCount = cart.length;
    
    console.log(`📊 عدد المنتجات المتوقع: ${expectedCount}`);
    
    return workingBadges > 0;
}

// فحص 5: صفحة السلة
function testCartPage() {
    console.log('\n5️⃣ فحص صفحة السلة:');
    
    const cartPage = document.getElementById('cartPage');
    if (!cartPage) {
        console.log('❌ صفحة السلة غير موجودة');
        return false;
    }
    
    console.log('✅ صفحة السلة موجودة');
    
    // فحص العناصر المهمة في صفحة السلة
    const cartElements = [
        'cartItems',
        'cartSummary',
        'emptyCart'
    ];
    
    let foundElements = 0;
    cartElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            console.log(`✅ ${elementId} - موجود`);
            foundElements++;
        } else {
            console.log(`❌ ${elementId} - مفقود`);
        }
    });
    
    return foundElements >= 2;
}

// فحص 6: وظائف المفضلة
function testWishlistFunctions() {
    console.log('\n6️⃣ فحص وظائف المفضلة:');
    
    const wishlistFunctions = [
        'addToWishlist',
        'removeFromWishlist',
        'toggleWishlist',
        'displayWishlist'
    ];
    
    let workingFunctions = 0;
    wishlistFunctions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ ${funcName}() - موجودة`);
            workingFunctions++;
        } else {
            console.log(`❌ ${funcName}() - مفقودة`);
        }
    });
    
    // فحص تخزين المفضلة
    try {
        const wishlist = JSON.parse(localStorage.getItem('wishlist') || '[]');
        console.log(`💖 عدد المنتجات في المفضلة: ${wishlist.length}`);
    } catch (error) {
        console.log(`❌ خطأ في قراءة المفضلة: ${error.message}`);
    }
    
    return workingFunctions >= 2;
}

// فحص 7: وظائف المقارنة
function testCompareFunctions() {
    console.log('\n7️⃣ فحص وظائف المقارنة:');
    
    const compareFunctions = [
        'addToCompare',
        'removeFromCompare',
        'clearCompare',
        'showCompare'
    ];
    
    let workingFunctions = 0;
    compareFunctions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ ${funcName}() - موجودة`);
            workingFunctions++;
        } else {
            console.log(`❌ ${funcName}() - مفقودة`);
        }
    });
    
    // فحص عداد المقارنة
    const compareBadge = document.getElementById('compareBadge');
    if (compareBadge) {
        console.log('✅ عداد المقارنة موجود');
    } else {
        console.log('❌ عداد المقارنة مفقود');
    }
    
    return workingFunctions >= 2;
}

// فحص 8: تجربة إضافة منتج للسلة
function testAddToCartFlow() {
    console.log('\n8️⃣ فحص تجربة إضافة منتج للسلة:');
    
    try {
        // البحث عن أول منتج في الصفحة
        const productCard = document.querySelector('.product-card');
        if (!productCard) {
            console.log('❌ لا توجد منتجات لاختبارها');
            return false;
        }
        
        // البحث عن زر "إضافة للسلة"
        const addToCartBtn = productCard.querySelector('.add-to-cart-btn, [onclick*="addToCart"]');
        if (!addToCartBtn) {
            console.log('❌ زر إضافة للسلة غير موجود');
            return false;
        }
        
        console.log('✅ زر إضافة للسلة موجود');
        
        // فحص إذا كان الزر يعمل (بدون تشغيله فعلياً)
        const onclickAttr = addToCartBtn.getAttribute('onclick');
        if (onclickAttr && onclickAttr.includes('addToCart')) {
            console.log('✅ زر إضافة للسلة مربوط بالدالة الصحيحة');
            return true;
        } else {
            console.log('⚠️ زر إضافة للسلة قد لا يعمل بشكل صحيح');
            return false;
        }
        
    } catch (error) {
        console.log(`❌ خطأ في فحص إضافة المنتج: ${error.message}`);
        return false;
    }
}

// تشغيل جميع اختبارات التسوق
function runShoppingTests() {
    console.log('🛒 بدء فحص شامل لوظائف التسوق...\n');
    
    const results = {
        basicCartFunctions: testBasicCartFunctions(),
        cartStorage: testCartStorage(),
        cartCalculations: testCartCalculations(),
        cartBadge: testCartBadge(),
        cartPage: testCartPage(),
        wishlistFunctions: testWishlistFunctions(),
        compareFunctions: testCompareFunctions(),
        addToCartFlow: testAddToCartFlow()
    };
    
    // حساب النتائج
    const totalTests = Object.keys(results).length;
    const passedTests = Object.values(results).filter(result => result === true).length;
    const failedTests = totalTests - passedTests;
    const successRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    console.log('\n📊 ملخص نتائج فحص التسوق:');
    console.log('=====================================');
    console.log(`✅ نجح: ${passedTests}/${totalTests}`);
    console.log(`❌ فشل: ${failedTests}/${totalTests}`);
    console.log(`📈 معدل النجاح: ${successRate}%`);
    
    if (successRate >= 80) {
        console.log('🎉 وظائف التسوق تعمل بشكل ممتاز!');
    } else if (successRate >= 60) {
        console.log('⚠️ وظائف التسوق تحتاج لبعض التحسينات');
    } else {
        console.log('🚨 وظائف التسوق تحتاج لإصلاحات عاجلة');
    }
    
    return results;
}

// تشغيل الاختبارات
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runShoppingTests);
} else {
    runShoppingTests();
}

// تصدير للاستخدام الخارجي
window.runShoppingTests = runShoppingTests;
