{"name": "dalila-car-mobile", "version": "1.0.0", "description": "دليل كار - ت<PERSON><PERSON><PERSON>ق قطع غيار السيارات React Native", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace DalilaCarApp.xcworkspace -scheme DalilaCarApp -configuration Release archive"}, "keywords": ["auto-parts", "car-parts", "automotive", "ecommerce", "mobile-app", "react-native", "arabic", "dalila-car", "قطع-غيار", "سيارات", "دليل-كار"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://dalilacar.com"}, "license": "MIT", "homepage": "https://dalilacar.com", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "react-native-screens": "^3.25.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "react-native-reanimated": "^3.5.4", "react-native-vector-icons": "^10.0.2", "@fortawesome/react-native-fontawesome": "^0.3.0", "@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/free-regular-svg-icons": "^6.4.2", "mobx": "^6.10.2", "mobx-react": "^9.0.1", "axios": "^1.6.0", "react-native-splash-screen": "^3.3.0", "react-native-onboarding-swiper": "^1.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}}