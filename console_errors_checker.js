/**
 * فحص أخطاء الكونسول والشبكة
 * Console and Network Errors Checker
 */

console.log('🐛 بدء فحص أخطاء الكونسول والشبكة...');

// متغيرات لتتبع الأخطاء
let consoleErrors = [];
let networkErrors = [];
let warningMessages = [];
let performanceIssues = [];

// حفظ الدوال الأصلية
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalFetch = window.fetch;

// 1. فحص أخطاء JavaScript
function setupErrorTracking() {
    console.log('\n1️⃣ إعداد تتبع الأخطاء...');
    
    // تتبع أخطاء الكونسول
    console.error = function(...args) {
        const errorMessage = args.join(' ');
        consoleErrors.push({
            type: 'console.error',
            message: errorMessage,
            timestamp: new Date().toISOString(),
            stack: new Error().stack
        });
        originalConsoleError.apply(console, args);
    };
    
    // تتبع التحذيرات
    console.warn = function(...args) {
        const warningMessage = args.join(' ');
        warningMessages.push({
            type: 'console.warn',
            message: warningMessage,
            timestamp: new Date().toISOString()
        });
        originalConsoleWarn.apply(console, args);
    };
    
    // تتبع أخطاء JavaScript العامة
    window.addEventListener('error', function(event) {
        consoleErrors.push({
            type: 'javascript.error',
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            timestamp: new Date().toISOString(),
            stack: event.error ? event.error.stack : null
        });
    });
    
    // تتبع أخطاء Promise
    window.addEventListener('unhandledrejection', function(event) {
        consoleErrors.push({
            type: 'promise.rejection',
            message: event.reason,
            timestamp: new Date().toISOString()
        });
    });
    
    console.log('✅ تم إعداد تتبع الأخطاء');
}

// 2. فحص أخطاء الشبكة
function setupNetworkTracking() {
    console.log('\n2️⃣ إعداد تتبع أخطاء الشبكة...');
    
    // تتبع طلبات fetch
    window.fetch = function(...args) {
        const url = args[0];
        const startTime = performance.now();
        
        return originalFetch.apply(this, args)
            .then(response => {
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                if (!response.ok) {
                    networkErrors.push({
                        type: 'fetch.error',
                        url: url,
                        status: response.status,
                        statusText: response.statusText,
                        duration: duration,
                        timestamp: new Date().toISOString()
                    });
                }
                
                // فحص الأداء
                if (duration > 5000) {
                    performanceIssues.push({
                        type: 'slow.request',
                        url: url,
                        duration: duration,
                        timestamp: new Date().toISOString()
                    });
                }
                
                return response;
            })
            .catch(error => {
                networkErrors.push({
                    type: 'fetch.exception',
                    url: url,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
                throw error;
            });
    };
    
    console.log('✅ تم إعداد تتبع الشبكة');
}

// 3. فحص الموارد المفقودة
function checkMissingResources() {
    console.log('\n3️⃣ فحص الموارد المفقودة...');
    
    const images = document.querySelectorAll('img');
    const scripts = document.querySelectorAll('script[src]');
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
    
    let missingResources = [];
    
    // فحص الصور
    images.forEach((img, index) => {
        if (img.complete && img.naturalWidth === 0) {
            missingResources.push({
                type: 'missing.image',
                src: img.src,
                alt: img.alt || 'بدون نص بديل',
                element: `img[${index}]`
            });
        }
    });
    
    console.log(`📊 تم فحص ${images.length} صورة`);
    console.log(`📊 تم فحص ${scripts.length} ملف JavaScript`);
    console.log(`📊 تم فحص ${stylesheets.length} ملف CSS`);
    
    if (missingResources.length > 0) {
        console.log(`❌ وُجد ${missingResources.length} مورد مفقود:`);
        missingResources.forEach(resource => {
            console.log(`   - ${resource.type}: ${resource.src}`);
        });
        return false;
    } else {
        console.log('✅ جميع الموارد محملة بشكل صحيح');
        return true;
    }
}

// 4. فحص أخطاء DOM
function checkDOMErrors() {
    console.log('\n4️⃣ فحص أخطاء DOM...');
    
    let domErrors = [];
    
    // فحص العناصر المكررة بنفس ID
    const allIds = [];
    const elementsWithId = document.querySelectorAll('[id]');
    
    elementsWithId.forEach(element => {
        const id = element.id;
        if (allIds.includes(id)) {
            domErrors.push({
                type: 'duplicate.id',
                id: id,
                message: `معرف مكرر: ${id}`
            });
        } else {
            allIds.push(id);
        }
    });
    
    // فحص الروابط المكسورة
    const links = document.querySelectorAll('a[href]');
    links.forEach(link => {
        const href = link.href;
        if (href === '#' || href === 'javascript:void(0)' || href === '') {
            // هذه روابط عادية، لا تحتاج فحص
        } else if (href.startsWith('http') && !href.includes(window.location.hostname)) {
            // روابط خارجية - يمكن فحصها لاحقاً
        }
    });
    
    console.log(`📊 تم فحص ${elementsWithId.length} عنصر له معرف`);
    console.log(`📊 تم فحص ${links.length} رابط`);
    
    if (domErrors.length > 0) {
        console.log(`❌ وُجد ${domErrors.length} خطأ في DOM:`);
        domErrors.forEach(error => {
            console.log(`   - ${error.message}`);
        });
        return false;
    } else {
        console.log('✅ لا توجد أخطاء في DOM');
        return true;
    }
}

// 5. فحص أداء JavaScript
function checkJavaScriptPerformance() {
    console.log('\n5️⃣ فحص أداء JavaScript...');
    
    const performanceEntries = performance.getEntriesByType('navigation');
    if (performanceEntries.length > 0) {
        const navigation = performanceEntries[0];
        
        const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
        const loadComplete = navigation.loadEventEnd - navigation.loadEventStart;
        
        console.log(`⏱️ وقت تحميل DOM: ${domContentLoaded.toFixed(2)}ms`);
        console.log(`⏱️ وقت التحميل الكامل: ${loadComplete.toFixed(2)}ms`);
        
        if (domContentLoaded > 3000) {
            performanceIssues.push({
                type: 'slow.dom.loading',
                duration: domContentLoaded,
                message: 'تحميل DOM بطيء'
            });
        }
        
        if (loadComplete > 5000) {
            performanceIssues.push({
                type: 'slow.page.loading',
                duration: loadComplete,
                message: 'تحميل الصفحة بطيء'
            });
        }
    }
    
    // فحص استخدام الذاكرة
    if (performance.memory) {
        const memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
        console.log(`💾 استخدام الذاكرة: ${memoryUsage.toFixed(2)}MB`);
        
        if (memoryUsage > 100) {
            performanceIssues.push({
                type: 'high.memory.usage',
                usage: memoryUsage,
                message: 'استخدام ذاكرة عالي'
            });
        }
    }
    
    return performanceIssues.length === 0;
}

// 6. تقرير شامل بالأخطاء
function generateErrorReport() {
    console.log('\n📊 تقرير شامل بالأخطاء:');
    console.log('=====================================');
    
    // أخطاء JavaScript
    if (consoleErrors.length > 0) {
        console.log(`\n❌ أخطاء JavaScript (${consoleErrors.length}):`);
        consoleErrors.forEach((error, index) => {
            console.log(`   ${index + 1}. [${error.type}] ${error.message}`);
            if (error.filename) {
                console.log(`      الملف: ${error.filename}:${error.lineno}:${error.colno}`);
            }
        });
    } else {
        console.log('\n✅ لا توجد أخطاء JavaScript');
    }
    
    // أخطاء الشبكة
    if (networkErrors.length > 0) {
        console.log(`\n❌ أخطاء الشبكة (${networkErrors.length}):`);
        networkErrors.forEach((error, index) => {
            console.log(`   ${index + 1}. [${error.type}] ${error.url} - ${error.status || error.error}`);
        });
    } else {
        console.log('\n✅ لا توجد أخطاء في الشبكة');
    }
    
    // التحذيرات
    if (warningMessages.length > 0) {
        console.log(`\n⚠️ تحذيرات (${warningMessages.length}):`);
        warningMessages.forEach((warning, index) => {
            console.log(`   ${index + 1}. ${warning.message}`);
        });
    } else {
        console.log('\n✅ لا توجد تحذيرات');
    }
    
    // مشاكل الأداء
    if (performanceIssues.length > 0) {
        console.log(`\n⚡ مشاكل الأداء (${performanceIssues.length}):`);
        performanceIssues.forEach((issue, index) => {
            console.log(`   ${index + 1}. [${issue.type}] ${issue.message}`);
            if (issue.duration) {
                console.log(`      المدة: ${issue.duration.toFixed(2)}ms`);
            }
        });
    } else {
        console.log('\n✅ لا توجد مشاكل في الأداء');
    }
    
    // النتيجة الإجمالية
    const totalIssues = consoleErrors.length + networkErrors.length + performanceIssues.length;
    const totalWarnings = warningMessages.length;
    
    console.log('\n🎯 الملخص النهائي:');
    console.log(`❌ إجمالي الأخطاء: ${totalIssues}`);
    console.log(`⚠️ إجمالي التحذيرات: ${totalWarnings}`);
    
    if (totalIssues === 0) {
        console.log('🎉 التطبيق خالي من الأخطاء!');
        return 'excellent';
    } else if (totalIssues <= 3) {
        console.log('👍 التطبيق في حالة جيدة مع أخطاء قليلة');
        return 'good';
    } else if (totalIssues <= 10) {
        console.log('⚠️ التطبيق يحتاج لبعض الإصلاحات');
        return 'needs_improvement';
    } else {
        console.log('🚨 التطبيق يحتاج لإصلاحات عاجلة');
        return 'critical';
    }
}

// تشغيل الفحص الشامل
function runErrorCheck() {
    console.log('🐛 بدء فحص شامل للأخطاء...\n');
    
    // إعداد التتبع
    setupErrorTracking();
    setupNetworkTracking();
    
    // انتظار قليل لتجميع الأخطاء
    setTimeout(() => {
        const results = {
            missingResources: checkMissingResources(),
            domErrors: checkDOMErrors(),
            performance: checkJavaScriptPerformance()
        };
        
        // تقرير نهائي
        const overallStatus = generateErrorReport();
        
        // استعادة الدوال الأصلية
        console.error = originalConsoleError;
        console.warn = originalConsoleWarn;
        window.fetch = originalFetch;
        
        return {
            status: overallStatus,
            consoleErrors: consoleErrors.length,
            networkErrors: networkErrors.length,
            warnings: warningMessages.length,
            performanceIssues: performanceIssues.length,
            results: results
        };
    }, 3000);
}

// تشغيل الفحص
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runErrorCheck);
} else {
    runErrorCheck();
}

// تصدير للاستخدام الخارجي
window.runErrorCheck = runErrorCheck;
window.getErrorSummary = function() {
    return {
        consoleErrors,
        networkErrors,
        warningMessages,
        performanceIssues
    };
};
